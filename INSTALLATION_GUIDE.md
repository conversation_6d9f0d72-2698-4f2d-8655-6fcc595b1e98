# Beauty Salon Management System - Installation Guide

## 🎯 Quick Start (Recommended)

### Step 1: Database Setup
1. Run the installation wizard: `http://localhost/saloon_parlour/install.php`
2. Follow the setup instructions to create the database

### Step 2: Fix Database Issues
1. Run the database fix script: `http://localhost/saloon_parlour/fix_database.php`
2. This will:
   - Fix foreign key constraints
   - Create sample users (admin, staff, customers)
   - Add sample services
   - Set up proper database structure

### Step 3: Test All Pages
1. Run the test script: `http://localhost/saloon_parlour/test_all_pages.php`
2. Verify all pages load without errors

### Step 4: Start Using the Application
1. Go to: `http://localhost/saloon_parlour/`
2. Login with these credentials:

**Admin Access:**
- Email: `<EMAIL>`
- Password: `password`

**Staff Access:**
- Email: `<EMAIL>` (or any staff email)
- Password: `password`

**Customer Access:**
- Email: `<EMAIL>` (or any customer email)
- Password: `password`

---

## 🔧 Manual Installation (Advanced)

### Prerequisites
- PHP 8.0 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- XAMPP/WAMP (for local development)

### Database Setup
1. Create a new MySQL database named `salon_management`
2. Import the database schema from `database/schema.sql`
3. Update database credentials in `config/config.php`

### File Permissions
Ensure the following directories are writable:
- `uploads/` (if exists)
- `temp/` (if exists)

### Configuration
1. Copy `config/config.example.php` to `config/config.php`
2. Update database settings:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'salon_management');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

---

## 📋 Features Overview

### ✅ Completed Features

#### **Authentication & User Management**
- Multi-role login (Admin, Staff, Customer)
- Secure password hashing
- Role-based access control
- User registration for customers

#### **Booking System**
- Online appointment booking
- Staff assignment
- Time slot management
- Booking status tracking
- Booking confirmation page

#### **Service Management**
- Service CRUD operations
- Service categories
- Pricing and duration management
- Beauty packages with discounts

#### **Customer Management**
- Customer profiles
- Booking history
- Loyalty program with points and tiers
- Review and rating system

#### **Staff Management**
- Staff profiles and specializations
- Shift management
- Performance tracking
- Commission calculations

#### **Business Operations**
- Invoice generation with PDF export
- Inventory management with stock alerts
- Promotion and discount system
- Social media management dashboard

#### **Analytics & Reporting**
- Dashboard with statistics
- Revenue reports
- Customer analytics
- Staff performance metrics

#### **Additional Features**
- Beauty gallery with before/after photos
- Help and support system
- Comprehensive settings panel
- Mobile-responsive design

---

## 🎨 Design Features

### **Feminine Theme**
- Pink/magenta color scheme (#E91E63, #F8BBD0, #FCE4EC)
- Elegant gradients and animations
- Beauty-focused icons and terminology
- Glass-morphism effects

### **Responsive Design**
- Mobile-friendly sidebar navigation
- Touch-optimized interface
- Adaptive layouts for all screen sizes

### **User Experience**
- Smooth animations and transitions
- Intuitive navigation structure
- Beautiful card-based layouts
- Interactive elements with hover effects

---

## 🗂️ File Structure

```
saloon_parlour/
├── 📁 assets/
│   ├── css/style.css          # Main stylesheet
│   └── js/script.js           # JavaScript functions
├── 📁 classes/
│   ├── Database.php           # Database connection
│   ├── User.php              # User management
│   ├── Booking.php           # Booking operations
│   ├── Service.php           # Service management
│   ├── Staff.php             # Staff operations
│   └── Invoice.php           # Invoice generation
├── 📁 config/
│   └── config.php            # Configuration settings
├── 📁 database/
│   └── schema.sql            # Database structure
├── 📁 includes/
│   ├── navbar.php            # Sidebar navigation
│   ├── functions.php         # Helper functions
│   └── auth.php              # Authentication functions
├── 📁 pages/
│   ├── 📁 auth/              # Login/Register pages
│   ├── 📁 dashboard/         # Dashboard
│   ├── 📁 bookings/          # Appointment management
│   ├── 📁 services/          # Service management
│   ├── 📁 packages/          # Beauty packages
│   ├── 📁 products/          # Product catalog
│   ├── 📁 customers/         # Client management
│   ├── 📁 loyalty/           # Loyalty program
│   ├── 📁 reviews/           # Reviews & ratings
│   ├── 📁 staff/             # Staff management
│   ├── 📁 invoices/          # Billing system
│   ├── 📁 inventory/         # Stock management
│   ├── 📁 reports/           # Analytics
│   ├── 📁 promotions/        # Offers & discounts
│   ├── 📁 gallery/           # Photo gallery
│   ├── 📁 social/            # Social media
│   ├── 📁 settings/          # Configuration
│   ├── 📁 help/              # Support system
│   ├── 📁 profile/           # User profiles
│   ├── home.php              # Landing page
│   └── booking_success.php   # Booking confirmation
├── install.php               # Installation wizard
├── fix_database.php          # Database fix script
├── test_all_pages.php        # Page testing script
└── index.php                 # Main application entry
```

---

## 🚀 Getting Started

1. **Run Database Fix**: `http://localhost/saloon_parlour/fix_database.php`
2. **Test Pages**: `http://localhost/saloon_parlour/test_all_pages.php`
3. **Login**: `http://localhost/saloon_parlour/` with <EMAIL> / password
4. **Explore**: All 19 menu items are fully functional!

---

## 🆘 Troubleshooting

### Common Issues

**Database Connection Error:**
- Check database credentials in `config/config.php`
- Ensure MySQL service is running
- Verify database exists

**Foreign Key Constraint Error:**
- Run `fix_database.php` to resolve constraint issues
- This script fixes the booking system foreign keys

**Page Not Found:**
- Ensure all files are uploaded correctly
- Check file permissions
- Verify web server configuration

**Login Issues:**
- Use the default credentials provided above
- Run `fix_database.php` to create default users
- Check session configuration

### Support
For additional support, check the Help & Support section in the application or contact the development team.

---

## 🎉 Enjoy Your Beauty Salon Management System!

Your comprehensive salon management system is now ready with all features working perfectly. The system includes everything needed to run a modern beauty salon or parlour business efficiently.
