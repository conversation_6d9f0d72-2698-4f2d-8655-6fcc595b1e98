# Salon/Parlour Management System

A comprehensive web-based management system for salons and parlours built with PHP 8+, MySQL, HTML, CSS, JavaScript, and Bootstrap.

## Features

### 🔐 Authentication System
- **Multi-role login**: Admin, Staff, and Customer accounts
- **Secure authentication** with password hashing
- **Role-based access control** for different user types
- **User registration** for customers

### 🛠️ Services Management
- **CRUD operations** for services (Create, Read, Update, Delete)
- **Service categories** organization
- **Pricing and duration** management
- **Service search and filtering**

### 📅 Booking System
- **Online booking** for customers
- **Staff assignment** to bookings
- **Date and time slot** management
- **Booking status tracking** (Pending, Confirmed, Completed, Cancelled)
- **Multiple services** per booking
- **Availability checking** to prevent double bookings

### 👥 Staff Management
- **Staff profiles** with specializations
- **Shift timing** management
- **Commission tracking**
- **Performance monitoring**

### 💰 Invoice & Payment System
- **Automatic invoice generation** upon booking completion
- **PDF invoice export** using TCPDF
- **Email notifications** with PHPMailer
- **Tax calculations** and payment tracking

### 📊 Reports & Analytics
- **Sales reports** (Daily, Weekly, Monthly)
- **Booking analytics** per service and staff
- **Revenue tracking** and performance metrics
- **Export functionality** for reports

### 🎛️ Admin Dashboard
- **Real-time statistics** overview
- **Quick action buttons** for common tasks
- **Recent bookings** display
- **Key performance indicators**

## Technology Stack

- **Backend**: PHP 8+
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6.0
- **PDF Generation**: TCPDF/FPDF
- **Email**: PHPMailer
- **Architecture**: MVC Pattern

## Installation

### Prerequisites
- PHP 8.0 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- PDO MySQL extension enabled

### Quick Installation

1. **Clone or download** the project to your web server directory:
   ```bash
   git clone <repository-url>
   cd saloon_parlour
   ```

2. **Run the installation wizard**:
   - Open your browser and navigate to: `http://localhost/saloon_parlour/install.php`
   - Follow the installation steps
   - Configure your database settings

3. **Complete setup**:
   - The installer will create the database and tables automatically
   - Default admin account will be created
   - Delete `install.php` file after installation for security

### Manual Installation

1. **Create MySQL database**:
   ```sql
   CREATE DATABASE salon_management;
   ```

2. **Import database schema**:
   ```bash
   mysql -u username -p salon_management < database/schema.sql
   ```

3. **Configure database connection**:
   - Edit `config/config.php`
   - Update database credentials

## Default Login Credentials

After installation, you can use these default accounts:

### Admin Account
- **Email**: <EMAIL>
- **Password**: password

### Staff Account
- **Email**: <EMAIL>
- **Password**: password

### Customer Account
- **Email**: <EMAIL>
- **Password**: password

> ⚠️ **Security Notice**: Change default passwords immediately after installation!

## Project Structure

```
saloon_parlour/
├── assets/
│   ├── css/
│   │   └── style.css          # Custom styles
│   └── js/
│       └── script.js          # Custom JavaScript
├── classes/
│   ├── User.php               # User management
│   ├── Service.php            # Service operations
│   ├── Booking.php            # Booking management
│   └── ServiceCategory.php    # Category management
├── config/
│   ├── config.php             # Application configuration
│   └── database.php           # Database connection
├── database/
│   └── schema.sql             # Database schema
├── includes/
│   └── navbar.php             # Navigation component
├── pages/
│   ├── auth/                  # Authentication pages
│   ├── dashboard/             # Dashboard
│   ├── bookings/              # Booking management
│   ├── services/              # Service management
│   ├── staff/                 # Staff management
│   └── reports/               # Reports and analytics
├── index.php                  # Main entry point
├── install.php                # Installation wizard
└── README.md                  # This file
```

## Usage Guide

### For Customers
1. **Register** a new account or **login** with existing credentials
2. **Browse services** and view pricing
3. **Book appointments** by selecting services, date, and time
4. **View booking history** and status updates
5. **Manage profile** information

### For Staff
1. **Login** with staff credentials
2. **View assigned bookings** and daily schedule
3. **Update booking status** as services are completed
4. **Access customer information** for appointments

### For Administrators
1. **Manage all aspects** of the system
2. **Add/edit services** and categories
3. **Manage staff accounts** and assignments
4. **View comprehensive reports** and analytics
5. **Handle customer bookings** and walk-ins
6. **Generate invoices** and track payments

## Configuration

### Email Settings (Optional)
To enable email notifications, update the SMTP settings in `config/config.php`:

```php
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');
```

### Customization
- **Styling**: Modify `assets/css/style.css` for custom themes
- **Business Hours**: Update time slots in booking forms
- **Currency**: Change currency symbol in `config/config.php`
- **Timezone**: Set appropriate timezone in configuration

## Security Features

- **Password hashing** using PHP's password_hash()
- **SQL injection protection** with prepared statements
- **XSS protection** with input sanitization
- **Session management** with secure practices
- **Role-based access control** for different user types

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments

## Version History

- **v1.0.0** - Initial release with core features
  - Authentication system
  - Service management
  - Booking system
  - Basic dashboard
  - Installation wizard

---

**Developed with ❤️ for salon and parlour businesses**
