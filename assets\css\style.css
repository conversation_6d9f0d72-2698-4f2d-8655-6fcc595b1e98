/* Salon Management System - Feminine Theme */

:root {
    --primary-color: #E91E63;
    --secondary-color: #F8BBD0;
    --accent-color: #FCE4EC;
    --tertiary-color: #AD1457;
    --quaternary-color: #FF69B4;
    --dark-color: #4A148C;
    --light-color: #FFF0F5;
    --success-color: #E91E63;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --info-color: #9C27B0;
    --sidebar-width: 280px;
    --header-height: 70px;
}

body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #FCE4EC 0%, #F8BBD0 50%, #E1BEE7 100%);
    background-attachment: fixed;
    color: var(--dark-color);
    margin: 0;
    padding: 0;
}

/* Sidebar Navigation */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--tertiary-color) 100%);
    box-shadow: 4px 0 15px rgba(233, 30, 99, 0.2);
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-header .brand {
    color: white;
    font-size: 24px;
    font-weight: 700;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.sidebar-header .brand i {
    font-size: 28px;
    background: linear-gradient(45deg, #FFB6C1, #FFF0F5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section-title {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0 20px 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: var(--quaternary-color);
    transform: translateX(5px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: var(--quaternary-color);
    box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.1);
}

.nav-link i {
    width: 20px;
    margin-right: 12px;
    font-size: 16px;
    text-align: center;
}

.nav-link .badge {
    margin-left: auto;
    background: var(--quaternary-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
}

/* User Profile Section in Sidebar */
.user-profile {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    margin-top: auto;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--quaternary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    font-weight: bold;
    margin: 0 auto 10px;
}

.user-info {
    text-align: center;
    color: white;
}

.user-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.user-role {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    padding: 30px;
    transition: all 0.3s ease;
}

/* Top Header */
.top-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    margin: -30px -30px 30px -30px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 20px rgba(233, 30, 99, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-title i {
    background: linear-gradient(45deg, var(--primary-color), var(--quaternary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Cards */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.1);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(233, 30, 99, 0.2);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    color: white;
    border-radius: 20px 20px 0 0 !important;
    font-weight: 600;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.card-body {
    padding: 25px;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    border: none;
    border-radius: 30px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: 30px;
    padding: 10px 28px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.3);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(139, 75, 140, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(139, 75, 140, 0.05);
}

/* Dashboard Stats */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    color: white;
    border-radius: 25px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(233, 30, 99, 0.4);
}

.stat-card .stat-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.stat-card .stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    opacity: 0.95;
    position: relative;
    z-index: 1;
}

.stat-card .stat-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    opacity: 0.3;
}

/* Login/Register Pages */
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--quaternary-color) 50%, var(--tertiary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 50px;
    box-shadow: 0 25px 50px rgba(233, 30, 99, 0.2);
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-logo {
    text-align: center;
    margin-bottom: 40px;
}

.auth-logo i {
    font-size: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--quaternary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    display: block;
}

.auth-logo h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 32px;
    margin-bottom: 10px;
    background: linear-gradient(45deg, var(--primary-color), var(--quaternary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-logo p {
    color: var(--tertiary-color);
    font-weight: 500;
    font-size: 16px;
}

/* Status Badges */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 500;
}

.badge-pending {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-confirmed {
    background-color: var(--info-color);
    color: white;
}

.badge-completed {
    background-color: var(--success-color);
    color: white;
}

.badge-cancelled {
    background-color: var(--danger-color);
    color: white;
}

/* No Sidebar Layout */
.main-content.no-sidebar {
    margin-left: 0;
}

/* Mobile Sidebar */
.sidebar-toggle {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: block;
    }

    .main-content {
        margin-left: 0;
        padding: 80px 15px 20px;
    }

    .top-header {
        margin: -80px -15px 30px -15px;
        padding: 15px;
        border-radius: 0 0 15px 15px;
    }

    .page-title {
        font-size: 24px;
    }

    .card {
        margin-bottom: 20px;
    }

    .stat-card .stat-number {
        font-size: 2rem;
    }

    .auth-card {
        padding: 30px 25px;
        margin: 10px;
        max-width: 100%;
    }

    .auth-logo h2 {
        font-size: 28px;
    }

    .auth-logo i {
        font-size: 50px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
    }

    .main-content {
        padding: 70px 10px 15px;
    }

    .top-header {
        margin: -70px -10px 20px -10px;
        padding: 10px;
    }

    .page-title {
        font-size: 20px;
    }

    .auth-card {
        padding: 25px 20px;
    }

    .card-body {
        padding: 20px;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
