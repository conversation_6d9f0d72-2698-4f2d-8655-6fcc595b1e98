<?php
/**
 * Booking Class
 * Handles booking management operations
 */

class Booking {
    private $conn;
    private $table_name = "bookings";

    public $id;
    public $customer_id;
    public $staff_id;
    public $booking_date;
    public $booking_time;
    public $status;
    public $total_amount;
    public $notes;
    public $created_by;
    public $created_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create new booking
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET customer_id=:customer_id, staff_user_id=:staff_user_id, booking_date=:booking_date,
                      booking_time=:booking_time, total_amount=:total_amount, notes=:notes,
                      created_by=:created_by";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->customer_id = htmlspecialchars(strip_tags($this->customer_id));
        $this->staff_id = htmlspecialchars(strip_tags($this->staff_id));
        $this->booking_date = htmlspecialchars(strip_tags($this->booking_date));
        $this->booking_time = htmlspecialchars(strip_tags($this->booking_time));
        $this->total_amount = htmlspecialchars(strip_tags($this->total_amount));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        $this->created_by = htmlspecialchars(strip_tags($this->created_by));

        // Bind values
        $stmt->bindParam(":customer_id", $this->customer_id);

        // Handle null staff_user_id properly
        if (empty($this->staff_id)) {
            $stmt->bindValue(":staff_user_id", null, PDO::PARAM_NULL);
        } else {
            $stmt->bindParam(":staff_user_id", $this->staff_id);
        }

        $stmt->bindParam(":booking_date", $this->booking_date);
        $stmt->bindParam(":booking_time", $this->booking_time);
        $stmt->bindParam(":total_amount", $this->total_amount);
        $stmt->bindParam(":notes", $this->notes);
        $stmt->bindParam(":created_by", $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Add services to booking
    public function addServices($services) {
        $query = "INSERT INTO booking_services (booking_id, service_id, price) VALUES (?, ?, ?)";
        $stmt = $this->conn->prepare($query);

        foreach ($services as $service) {
            $stmt->execute([$this->id, $service['id'], $service['price']]);
        }

        return true;
    }

    // Read all bookings
    public function readAll() {
        $query = "SELECT b.*, u.name as customer_name, u.phone as customer_phone,
                         su.name as staff_name
                  FROM " . $this->table_name . " b
                  LEFT JOIN users u ON b.customer_id = u.id
                  LEFT JOIN users su ON b.staff_user_id = su.id
                  ORDER BY b.booking_date DESC, b.booking_time DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // Read bookings by date
    public function readByDate($date) {
        $query = "SELECT b.*, u.name as customer_name, u.phone as customer_phone,
                         s.user_id, su.name as staff_name
                  FROM " . $this->table_name . " b 
                  LEFT JOIN users u ON b.customer_id = u.id 
                  LEFT JOIN staff s ON b.staff_id = s.id 
                  LEFT JOIN users su ON s.user_id = su.id 
                  WHERE b.booking_date = :date 
                  ORDER BY b.booking_time";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":date", $date);
        $stmt->execute();

        return $stmt;
    }

    // Read bookings by customer
    public function readByCustomer($customer_id) {
        $query = "SELECT b.*, u.name as customer_name, u.phone as customer_phone,
                         s.user_id, su.name as staff_name
                  FROM " . $this->table_name . " b 
                  LEFT JOIN users u ON b.customer_id = u.id 
                  LEFT JOIN staff s ON b.staff_id = s.id 
                  LEFT JOIN users su ON s.user_id = su.id 
                  WHERE b.customer_id = :customer_id 
                  ORDER BY b.booking_date DESC, b.booking_time DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":customer_id", $customer_id);
        $stmt->execute();

        return $stmt;
    }

    // Read single booking
    public function readOne() {
        $query = "SELECT b.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone,
                         su.name as staff_name
                  FROM " . $this->table_name . " b
                  LEFT JOIN users u ON b.customer_id = u.id
                  LEFT JOIN users su ON b.staff_user_id = su.id
                  WHERE b.id = :id LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->customer_id = $row['customer_id'];
            $this->staff_id = $row['staff_id'];
            $this->booking_date = $row['booking_date'];
            $this->booking_time = $row['booking_time'];
            $this->status = $row['status'];
            $this->total_amount = $row['total_amount'];
            $this->notes = $row['notes'];
            $this->created_by = $row['created_by'];
            $this->created_at = $row['created_at'];
            return $row;
        }

        return false;
    }

    // Get booking services
    public function getServices() {
        $query = "SELECT bs.*, s.name as service_name, s.description 
                  FROM booking_services bs 
                  LEFT JOIN services s ON bs.service_id = s.id 
                  WHERE bs.booking_id = :booking_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":booking_id", $this->id);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    // Update booking
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET customer_id=:customer_id, staff_user_id=:staff_user_id, booking_date=:booking_date,
                      booking_time=:booking_time, status=:status, total_amount=:total_amount,
                      notes=:notes
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->customer_id = htmlspecialchars(strip_tags($this->customer_id));
        $this->staff_id = htmlspecialchars(strip_tags($this->staff_id));
        $this->booking_date = htmlspecialchars(strip_tags($this->booking_date));
        $this->booking_time = htmlspecialchars(strip_tags($this->booking_time));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->total_amount = htmlspecialchars(strip_tags($this->total_amount));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind values
        $stmt->bindParam(":customer_id", $this->customer_id);

        // Handle null staff_user_id properly
        if (empty($this->staff_id)) {
            $stmt->bindValue(":staff_user_id", null, PDO::PARAM_NULL);
        } else {
            $stmt->bindParam(":staff_user_id", $this->staff_id);
        }

        $stmt->bindParam(":booking_date", $this->booking_date);
        $stmt->bindParam(":booking_time", $this->booking_time);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":total_amount", $this->total_amount);
        $stmt->bindParam(":notes", $this->notes);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Update booking status
    public function updateStatus($status) {
        $query = "UPDATE " . $this->table_name . " SET status=:status WHERE id=:id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Delete booking
    public function delete() {
        // First delete booking services
        $query = "DELETE FROM booking_services WHERE booking_id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        // Then delete booking
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Check time slot availability
    public function isTimeSlotAvailable($date, $time, $staff_id = null, $exclude_booking_id = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE booking_date = :date AND booking_time = :time 
                  AND status NOT IN ('cancelled')";
        
        $params = [':date' => $date, ':time' => $time];

        if ($staff_id) {
            $query .= " AND staff_id = :staff_id";
            $params[':staff_id'] = $staff_id;
        }

        if ($exclude_booking_id) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $exclude_booking_id;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);

        return $stmt->fetch()['count'] == 0;
    }

    // Get booking statistics
    public function getStats() {
        $stats = [];

        // Total bookings
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch()['count'];

        // Today's bookings
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE DATE(booking_date) = CURDATE()";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['today'] = $stmt->fetch()['count'];

        // Pending bookings
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE status = 'pending'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['pending'] = $stmt->fetch()['count'];

        return $stats;
    }

    // Get available staff for a specific date and time
    public function getAvailableStaff($date, $time) {
        $query = "SELECT u.id, u.name
                  FROM users u
                  WHERE u.role = 'staff' AND u.status = 'active'
                  AND u.id NOT IN (
                      SELECT DISTINCT b.staff_user_id
                      FROM " . $this->table_name . " b
                      WHERE b.booking_date = :date
                      AND b.booking_time = :time
                      AND b.status NOT IN ('cancelled')
                      AND b.staff_user_id IS NOT NULL
                  )
                  ORDER BY u.name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":date", $date);
        $stmt->bindParam(":time", $time);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}
?>
