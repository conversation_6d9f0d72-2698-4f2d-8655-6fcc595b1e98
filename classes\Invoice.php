<?php
/**
 * Invoice Class
 * Handles invoice generation and management
 */

class Invoice {
    private $conn;
    private $table_name = "invoices";

    public $id;
    public $booking_id;
    public $invoice_number;
    public $subtotal;
    public $tax_rate;
    public $tax_amount;
    public $discount_amount;
    public $total_amount;
    public $payment_status;
    public $payment_method;
    public $created_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Generate invoice for booking
    public function generateFromBooking($booking_id) {
        // Get booking details
        $query = "SELECT b.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone
                  FROM bookings b 
                  LEFT JOIN users u ON b.customer_id = u.id 
                  WHERE b.id = :booking_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":booking_id", $booking_id);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return false;
        }
        
        $booking = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get booking services
        $query = "SELECT bs.*, s.name as service_name 
                  FROM booking_services bs 
                  LEFT JOIN services s ON bs.service_id = s.id 
                  WHERE bs.booking_id = :booking_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":booking_id", $booking_id);
        $stmt->execute();
        $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calculate amounts
        $subtotal = 0;
        foreach ($services as $service) {
            $subtotal += $service['price'];
        }
        
        $this->booking_id = $booking_id;
        $this->invoice_number = $this->generateInvoiceNumber();
        $this->subtotal = $subtotal;
        $this->tax_rate = 18.00; // Default GST rate
        $this->tax_amount = ($subtotal * $this->tax_rate) / 100;
        $this->discount_amount = 0.00;
        $this->total_amount = $subtotal + $this->tax_amount - $this->discount_amount;
        $this->payment_status = 'pending';
        $this->payment_method = 'cash';
        
        return $this->create();
    }

    // Create new invoice
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET booking_id=:booking_id, invoice_number=:invoice_number, 
                      subtotal=:subtotal, tax_rate=:tax_rate, tax_amount=:tax_amount, 
                      discount_amount=:discount_amount, total_amount=:total_amount, 
                      payment_status=:payment_status, payment_method=:payment_method";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->booking_id = htmlspecialchars(strip_tags($this->booking_id));
        $this->invoice_number = htmlspecialchars(strip_tags($this->invoice_number));
        $this->subtotal = htmlspecialchars(strip_tags($this->subtotal));
        $this->tax_rate = htmlspecialchars(strip_tags($this->tax_rate));
        $this->tax_amount = htmlspecialchars(strip_tags($this->tax_amount));
        $this->discount_amount = htmlspecialchars(strip_tags($this->discount_amount));
        $this->total_amount = htmlspecialchars(strip_tags($this->total_amount));
        $this->payment_status = htmlspecialchars(strip_tags($this->payment_status));
        $this->payment_method = htmlspecialchars(strip_tags($this->payment_method));

        // Bind values
        $stmt->bindParam(":booking_id", $this->booking_id);
        $stmt->bindParam(":invoice_number", $this->invoice_number);
        $stmt->bindParam(":subtotal", $this->subtotal);
        $stmt->bindParam(":tax_rate", $this->tax_rate);
        $stmt->bindParam(":tax_amount", $this->tax_amount);
        $stmt->bindParam(":discount_amount", $this->discount_amount);
        $stmt->bindParam(":total_amount", $this->total_amount);
        $stmt->bindParam(":payment_status", $this->payment_status);
        $stmt->bindParam(":payment_method", $this->payment_method);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read all invoices
    public function readAll() {
        $query = "SELECT i.*, b.booking_date, b.booking_time, u.name as customer_name 
                  FROM " . $this->table_name . " i 
                  LEFT JOIN bookings b ON i.booking_id = b.id 
                  LEFT JOIN users u ON b.customer_id = u.id 
                  ORDER BY i.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // Read single invoice
    public function readOne() {
        $query = "SELECT i.*, b.booking_date, b.booking_time, b.notes,
                         u.name as customer_name, u.email as customer_email, u.phone as customer_phone
                  FROM " . $this->table_name . " i 
                  LEFT JOIN bookings b ON i.booking_id = b.id 
                  LEFT JOIN users u ON b.customer_id = u.id 
                  WHERE i.id = :id LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->booking_id = $row['booking_id'];
            $this->invoice_number = $row['invoice_number'];
            $this->subtotal = $row['subtotal'];
            $this->tax_rate = $row['tax_rate'];
            $this->tax_amount = $row['tax_amount'];
            $this->discount_amount = $row['discount_amount'];
            $this->total_amount = $row['total_amount'];
            $this->payment_status = $row['payment_status'];
            $this->payment_method = $row['payment_method'];
            $this->created_at = $row['created_at'];
            return $row;
        }

        return false;
    }

    // Get invoice services
    public function getServices() {
        $query = "SELECT bs.*, s.name as service_name, s.description 
                  FROM booking_services bs 
                  LEFT JOIN services s ON bs.service_id = s.id 
                  WHERE bs.booking_id = :booking_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":booking_id", $this->booking_id);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    // Update payment status
    public function updatePaymentStatus($status, $method = null) {
        $query = "UPDATE " . $this->table_name . " SET payment_status=:status";
        
        if ($method) {
            $query .= ", payment_method=:method";
        }
        
        $query .= " WHERE id=:id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":id", $this->id);
        
        if ($method) {
            $stmt->bindParam(":method", $method);
        }

        return $stmt->execute();
    }

    // Generate unique invoice number
    private function generateInvoiceNumber() {
        $prefix = 'INV';
        $year = date('Y');
        $month = date('m');
        
        // Get last invoice number for this month
        $query = "SELECT invoice_number FROM " . $this->table_name . " 
                  WHERE invoice_number LIKE :pattern 
                  ORDER BY id DESC LIMIT 1";
        
        $pattern = $prefix . $year . $month . '%';
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":pattern", $pattern);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $last_invoice = $stmt->fetch()['invoice_number'];
            $last_number = intval(substr($last_invoice, -4));
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . $year . $month . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    // Check if invoice exists for booking
    public function existsForBooking($booking_id) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE booking_id = :booking_id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":booking_id", $booking_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $this->id = $stmt->fetch()['id'];
            return true;
        }

        return false;
    }

    // Get invoice statistics
    public function getStats() {
        $stats = [];

        // Total invoices
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch()['count'];

        // Paid invoices
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE payment_status = 'paid'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['paid'] = $stmt->fetch()['count'];

        // Pending invoices
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE payment_status = 'pending'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['pending'] = $stmt->fetch()['count'];

        // Total revenue
        $query = "SELECT SUM(total_amount) as revenue FROM " . $this->table_name . " WHERE payment_status = 'paid'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['revenue'] = $stmt->fetch()['revenue'] ?? 0;

        return $stats;
    }
}
?>
