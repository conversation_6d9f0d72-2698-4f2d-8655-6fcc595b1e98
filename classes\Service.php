<?php
/**
 * Service Class
 * Handles service management operations
 */

class Service {
    private $conn;
    private $table_name = "services";

    public $id;
    public $name;
    public $description;
    public $price;
    public $duration;
    public $category_id;
    public $status;
    public $created_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create new service
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET name=:name, description=:description, price=:price, 
                      duration=:duration, category_id=:category_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->duration = htmlspecialchars(strip_tags($this->duration));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));

        // Bind values
        $stmt->bindParam(":name", $this->name);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":duration", $this->duration);
        $stmt->bindParam(":category_id", $this->category_id);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read all services
    public function readAll() {
        $query = "SELECT s.*, sc.name as category_name 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN service_categories sc ON s.category_id = sc.id 
                  WHERE s.status = 'active' 
                  ORDER BY sc.name, s.name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // Read active services
    public function readActive() {
        $query = "SELECT s.*, sc.name as category_name 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN service_categories sc ON s.category_id = sc.id 
                  WHERE s.status = 'active' 
                  ORDER BY sc.name, s.name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // Read services by category
    public function readByCategory($category_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE category_id = :category_id AND status = 'active' 
                  ORDER BY name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":category_id", $category_id);
        $stmt->execute();

        return $stmt;
    }

    // Read single service
    public function readOne() {
        $query = "SELECT s.*, sc.name as category_name 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN service_categories sc ON s.category_id = sc.id 
                  WHERE s.id = :id LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->name = $row['name'];
            $this->description = $row['description'];
            $this->price = $row['price'];
            $this->duration = $row['duration'];
            $this->category_id = $row['category_id'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            return true;
        }

        return false;
    }

    // Update service
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET name=:name, description=:description, price=:price, 
                      duration=:duration, category_id=:category_id 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->duration = htmlspecialchars(strip_tags($this->duration));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind values
        $stmt->bindParam(":name", $this->name);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":duration", $this->duration);
        $stmt->bindParam(":category_id", $this->category_id);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Delete service (soft delete)
    public function delete() {
        $query = "UPDATE " . $this->table_name . " SET status='inactive' WHERE id=:id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Search services
    public function search($keywords) {
        $query = "SELECT s.*, sc.name as category_name 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN service_categories sc ON s.category_id = sc.id 
                  WHERE s.status = 'active' 
                  AND (s.name LIKE :keywords OR s.description LIKE :keywords OR sc.name LIKE :keywords) 
                  ORDER BY s.name";

        $stmt = $this->conn->prepare($query);
        $keywords = "%{$keywords}%";
        $stmt->bindParam(":keywords", $keywords);
        $stmt->execute();

        return $stmt;
    }

    // Get service statistics
    public function getStats() {
        $stats = [];

        // Total services
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch()['count'];

        // Most popular service
        $query = "SELECT s.name, COUNT(bs.service_id) as booking_count 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN booking_services bs ON s.id = bs.service_id 
                  WHERE s.status = 'active' 
                  GROUP BY s.id, s.name 
                  ORDER BY booking_count DESC 
                  LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $popular = $stmt->fetch();
        $stats['most_popular'] = $popular ? $popular['name'] : 'N/A';

        // Average price
        $query = "SELECT AVG(price) as avg_price FROM " . $this->table_name . " WHERE status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['avg_price'] = $stmt->fetch()['avg_price'];

        return $stats;
    }
}
?>
