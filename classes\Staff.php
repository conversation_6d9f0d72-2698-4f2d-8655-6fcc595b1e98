<?php
/**
 * Staff Class
 * Handles staff management operations
 */

class Staff {
    private $conn;
    private $table_name = "staff";

    public $id;
    public $user_id;
    public $specialization;
    public $shift_start;
    public $shift_end;
    public $commission_rate;
    public $status;
    public $created_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create new staff member
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, specialization=:specialization, 
                      shift_start=:shift_start, shift_end=:shift_end, 
                      commission_rate=:commission_rate";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $this->specialization = htmlspecialchars(strip_tags($this->specialization));
        $this->shift_start = htmlspecialchars(strip_tags($this->shift_start));
        $this->shift_end = htmlspecialchars(strip_tags($this->shift_end));
        $this->commission_rate = htmlspecialchars(strip_tags($this->commission_rate));

        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":specialization", $this->specialization);
        $stmt->bindParam(":shift_start", $this->shift_start);
        $stmt->bindParam(":shift_end", $this->shift_end);
        $stmt->bindParam(":commission_rate", $this->commission_rate);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read all staff
    public function readAll() {
        $query = "SELECT s.*, u.name, u.email, u.phone, u.status as user_status 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE s.status = 'active' 
                  ORDER BY u.name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // Read active staff
    public function readActive() {
        $query = "SELECT s.*, u.name, u.email, u.phone 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE s.status = 'active' AND u.status = 'active' 
                  ORDER BY u.name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // Read single staff member
    public function readOne() {
        $query = "SELECT s.*, u.name, u.email, u.phone, u.status as user_status 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE s.id = :id LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->user_id = $row['user_id'];
            $this->specialization = $row['specialization'];
            $this->shift_start = $row['shift_start'];
            $this->shift_end = $row['shift_end'];
            $this->commission_rate = $row['commission_rate'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            return $row;
        }

        return false;
    }

    // Update staff member
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET specialization=:specialization, shift_start=:shift_start, 
                      shift_end=:shift_end, commission_rate=:commission_rate 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->specialization = htmlspecialchars(strip_tags($this->specialization));
        $this->shift_start = htmlspecialchars(strip_tags($this->shift_start));
        $this->shift_end = htmlspecialchars(strip_tags($this->shift_end));
        $this->commission_rate = htmlspecialchars(strip_tags($this->commission_rate));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind values
        $stmt->bindParam(":specialization", $this->specialization);
        $stmt->bindParam(":shift_start", $this->shift_start);
        $stmt->bindParam(":shift_end", $this->shift_end);
        $stmt->bindParam(":commission_rate", $this->commission_rate);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Delete staff member (soft delete)
    public function delete() {
        $query = "UPDATE " . $this->table_name . " SET status='inactive' WHERE id=:id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Get staff by user ID
    public function readByUserId($user_id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE user_id = :user_id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->id = $row['id'];
            $this->user_id = $row['user_id'];
            $this->specialization = $row['specialization'];
            $this->shift_start = $row['shift_start'];
            $this->shift_end = $row['shift_end'];
            $this->commission_rate = $row['commission_rate'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            return true;
        }

        return false;
    }

    // Get staff performance
    public function getPerformance($staff_id, $start_date = null, $end_date = null) {
        $where_clause = "WHERE b.staff_id = :staff_id AND b.status = 'completed'";
        $params = [':staff_id' => $staff_id];

        if ($start_date && $end_date) {
            $where_clause .= " AND b.booking_date BETWEEN :start_date AND :end_date";
            $params[':start_date'] = $start_date;
            $params[':end_date'] = $end_date;
        }

        // Total bookings
        $query = "SELECT COUNT(*) as total_bookings, 
                         SUM(b.total_amount) as total_revenue,
                         AVG(b.total_amount) as avg_booking_value
                  FROM bookings b $where_clause";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $performance = $stmt->fetch(PDO::FETCH_ASSOC);

        // Commission earned
        $commission_rate = $this->commission_rate ?? 0;
        $performance['commission_earned'] = ($performance['total_revenue'] * $commission_rate) / 100;

        return $performance;
    }

    // Get staff schedule for a date
    public function getSchedule($staff_id, $date) {
        $query = "SELECT b.*, u.name as customer_name, u.phone as customer_phone
                  FROM bookings b 
                  LEFT JOIN users u ON b.customer_id = u.id 
                  WHERE b.staff_id = :staff_id AND b.booking_date = :date 
                  AND b.status NOT IN ('cancelled') 
                  ORDER BY b.booking_time";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":staff_id", $staff_id);
        $stmt->bindParam(":date", $date);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    // Check if user is already a staff member
    public function userIsStaff($user_id) {
        $query = "SELECT id FROM " . $this->table_name . " 
                  WHERE user_id = :user_id AND status = 'active' LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    // Get available staff for a time slot
    public function getAvailableStaff($date, $time) {
        $query = "SELECT s.*, u.name, u.email 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE s.status = 'active' AND u.status = 'active'
                  AND s.shift_start <= :time AND s.shift_end >= :time
                  AND s.id NOT IN (
                      SELECT DISTINCT staff_id FROM bookings 
                      WHERE booking_date = :date AND booking_time = :time 
                      AND status NOT IN ('cancelled') AND staff_id IS NOT NULL
                  )
                  ORDER BY u.name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":date", $date);
        $stmt->bindParam(":time", $time);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    // Get staff statistics
    public function getStats() {
        $stats = [];

        // Total active staff
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch()['count'];

        // Staff with bookings today
        $query = "SELECT COUNT(DISTINCT b.staff_id) as count 
                  FROM bookings b 
                  WHERE DATE(b.booking_date) = CURDATE() 
                  AND b.status NOT IN ('cancelled')";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['working_today'] = $stmt->fetch()['count'];

        // Top performer this month
        $query = "SELECT s.id, u.name, COUNT(b.id) as booking_count 
                  FROM " . $this->table_name . " s 
                  LEFT JOIN users u ON s.user_id = u.id 
                  LEFT JOIN bookings b ON s.id = b.staff_id 
                  WHERE MONTH(b.booking_date) = MONTH(CURDATE()) 
                  AND YEAR(b.booking_date) = YEAR(CURDATE())
                  AND b.status = 'completed'
                  GROUP BY s.id, u.name 
                  ORDER BY booking_count DESC 
                  LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $top_performer = $stmt->fetch();
        $stats['top_performer'] = $top_performer ? $top_performer['name'] : 'N/A';

        return $stats;
    }
}
?>
