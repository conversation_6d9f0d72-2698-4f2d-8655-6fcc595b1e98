<?php
/**
 * Application Configuration
 * Salon/Parlour Management System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'Salon Management System');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/saloon_parlour/');

// Database settings
define('DB_HOST', 'localhost');
define('DB_NAME', 'salon_management');
define('DB_USER', 'root');
define('DB_PASS', '');

// Email settings (for P<PERSON><PERSON>ailer)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // Set your email
define('SMTP_PASSWORD', ''); // Set your app password
define('SMTP_ENCRYPTION', 'tls');

// File upload settings
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Include required files
require_once 'database.php';

// Helper functions
function redirect($url) {
    header("Location: " . BASE_URL . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getUserRole() {
    return $_SESSION['role'] ?? null;
}

function hasRole($role) {
    return getUserRole() === $role;
}

function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

function formatDate($date) {
    return date('d M Y', strtotime($date));
}

function formatDateTime($datetime) {
    return date('d M Y h:i A', strtotime($datetime));
}
?>
