-- Salon/Parlour Management System Database Schema
-- Created: 2025-08-22

CREATE DATABASE IF NOT EXISTS salon_management;
USE salon_management;

-- Users table (admin, staff, customers)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(15),
    role ENUM('admin', 'staff', 'customer') NOT NULL DEFAULT 'customer',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Service categories
CREATE TABLE service_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL COMMENT 'Duration in minutes',
    category_id INT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(id) ON DELETE SET NULL
);

-- Staff table
CREATE TABLE staff (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    specialization VARCHAR(255),
    shift_start TIME,
    shift_end TIME,
    commission_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Commission percentage',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    staff_id INT,
    booking_date DATE NOT NULL,
    booking_time TIME NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT,
    created_by INT COMMENT 'User who created the booking',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Booking services (many-to-many relationship)
CREATE TABLE booking_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    service_id INT NOT NULL,
    price DECIMAL(10,2) NOT NULL COMMENT 'Price at time of booking',
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- Invoices table
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_rate DECIMAL(5,2) DEFAULT 18.00,
    tax_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('pending', 'paid', 'partial', 'refunded') DEFAULT 'pending',
    payment_method ENUM('cash', 'card', 'upi', 'online') DEFAULT 'cash',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- Insert default data
-- Default admin user (password: password)
INSERT INTO users (name, email, password, role) VALUES
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('Staff Member', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'staff'),
('John Customer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'customer');

INSERT INTO service_categories (name, description) VALUES
('Hair Care', 'Hair cutting, styling, and treatment services'),
('Skin Care', 'Facial treatments and skin care services'),
('Beauty', 'Makeup and beauty enhancement services'),
('Spa', 'Relaxation and wellness services');

INSERT INTO services (name, description, price, duration, category_id) VALUES
('Hair Cut', 'Professional hair cutting service', 500.00, 45, 1),
('Hair Wash & Blow Dry', 'Hair washing and styling', 300.00, 30, 1),
('Hair Styling', 'Professional hair styling service', 400.00, 60, 1),
('Basic Facial', 'Deep cleansing facial treatment', 800.00, 60, 2),
('Anti-Aging Facial', 'Advanced anti-aging treatment', 1200.00, 90, 2),
('Bridal Makeup', 'Complete bridal makeup package', 5000.00, 120, 3),
('Party Makeup', 'Makeup for special occasions', 2000.00, 90, 3),
('Manicure', 'Hand and nail care treatment', 400.00, 45, 3),
('Pedicure', 'Foot and nail care treatment', 500.00, 60, 3),
('Full Body Massage', 'Relaxing full body massage', 1500.00, 90, 4),
('Head Massage', 'Stress relief head massage', 600.00, 30, 4);

-- Insert staff data
INSERT INTO staff (user_id, specialization, shift_start, shift_end, commission_rate) VALUES
(2, 'Hair Styling, Makeup', '09:00:00', '18:00:00', 15.00);
