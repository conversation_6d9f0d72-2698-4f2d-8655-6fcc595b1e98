<?php
/**
 * Database Fix Script
 * Fixes foreign key constraints and adds sample data
 */

require_once 'config/config.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Fixing Database Issues...</h2>";
    
    // First, let's drop the problematic foreign key constraint
    echo "<h3>1. Removing problematic foreign key constraint...</h3>";
    try {
        $db->exec("ALTER TABLE bookings DROP FOREIGN KEY bookings_ibfk_2");
        echo "✅ Removed foreign key constraint bookings_ibfk_2<br>";
    } catch (Exception $e) {
        echo "ℹ️ Foreign key constraint may not exist: " . $e->getMessage() . "<br>";
    }
    
    // Modify the bookings table to reference users table instead of staff table
    echo "<h3>2. Updating bookings table structure...</h3>";
    try {
        // Add new column for staff_user_id if it doesn't exist
        $db->exec("ALTER TABLE bookings ADD COLUMN staff_user_id INT NULL AFTER staff_id");
        echo "✅ Added staff_user_id column<br>";
    } catch (Exception $e) {
        echo "ℹ️ staff_user_id column may already exist: " . $e->getMessage() . "<br>";
    }
    
    // Add foreign key constraint to users table
    try {
        $db->exec("ALTER TABLE bookings ADD CONSTRAINT fk_bookings_staff_user 
                   FOREIGN KEY (staff_user_id) REFERENCES users(id) ON DELETE SET NULL");
        echo "✅ Added foreign key constraint to users table<br>";
    } catch (Exception $e) {
        echo "ℹ️ Foreign key constraint may already exist: " . $e->getMessage() . "<br>";
    }
    
    // Create sample admin user if not exists
    echo "<h3>3. Creating admin user...</h3>";
    $check_admin = $db->prepare("SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'");
    $check_admin->execute();
    
    if ($check_admin->fetch()['count'] == 0) {
        $admin_query = "INSERT INTO users (name, email, phone, password, role, status) 
                        VALUES ('Admin User', '<EMAIL>', '+91 98765 43210', :password, 'admin', 'active')";
        $admin_stmt = $db->prepare($admin_query);
        $admin_password = password_hash('password', PASSWORD_DEFAULT);
        $admin_stmt->bindParam(':password', $admin_password);
        
        if ($admin_stmt->execute()) {
            echo "✅ Created admin user (<EMAIL> / password)<br>";
        } else {
            echo "❌ Failed to create admin user<br>";
        }
    } else {
        echo "ℹ️ Admin user already exists<br>";
    }
    
    // Create sample staff users
    echo "<h3>4. Creating staff users...</h3>";
    $staff_members = [
        ['name' => 'Emily Rodriguez', 'email' => '<EMAIL>', 'phone' => '+91 98765 43211'],
        ['name' => 'Maria Garcia', 'email' => '<EMAIL>', 'phone' => '+91 98765 43212'],
        ['name' => 'Jennifer Davis', 'email' => '<EMAIL>', 'phone' => '+91 98765 43213'],
        ['name' => 'Sophie Chen', 'email' => '<EMAIL>', 'phone' => '+91 98765 43214']
    ];
    
    foreach ($staff_members as $member) {
        $check_staff = $db->prepare("SELECT COUNT(*) as count FROM users WHERE email = :email");
        $check_staff->bindParam(':email', $member['email']);
        $check_staff->execute();
        
        if ($check_staff->fetch()['count'] == 0) {
            $staff_query = "INSERT INTO users (name, email, phone, password, role, status) 
                            VALUES (:name, :email, :phone, :password, 'staff', 'active')";
            $staff_stmt = $db->prepare($staff_query);
            $staff_password = password_hash('password', PASSWORD_DEFAULT);
            
            $staff_stmt->bindParam(':name', $member['name']);
            $staff_stmt->bindParam(':email', $member['email']);
            $staff_stmt->bindParam(':phone', $member['phone']);
            $staff_stmt->bindParam(':password', $staff_password);
            
            if ($staff_stmt->execute()) {
                echo "✅ Created staff user: " . $member['name'] . "<br>";
            } else {
                echo "❌ Failed to create staff user: " . $member['name'] . "<br>";
            }
        } else {
            echo "ℹ️ Staff user already exists: " . $member['name'] . "<br>";
        }
    }
    
    // Create sample customer users
    echo "<h3>5. Creating customer users...</h3>";
    $customers = [
        ['name' => 'Sarah Johnson', 'email' => '<EMAIL>', 'phone' => '+91 98765 54321'],
        ['name' => 'Emma Wilson', 'email' => '<EMAIL>', 'phone' => '+91 98765 54322'],
        ['name' => 'Lisa Brown', 'email' => '<EMAIL>', 'phone' => '+91 98765 54323']
    ];
    
    foreach ($customers as $customer) {
        $check_customer = $db->prepare("SELECT COUNT(*) as count FROM users WHERE email = :email");
        $check_customer->bindParam(':email', $customer['email']);
        $check_customer->execute();
        
        if ($check_customer->fetch()['count'] == 0) {
            $customer_query = "INSERT INTO users (name, email, phone, password, role, status) 
                               VALUES (:name, :email, :phone, :password, 'customer', 'active')";
            $customer_stmt = $db->prepare($customer_query);
            $customer_password = password_hash('password', PASSWORD_DEFAULT);
            
            $customer_stmt->bindParam(':name', $customer['name']);
            $customer_stmt->bindParam(':email', $customer['email']);
            $customer_stmt->bindParam(':phone', $customer['phone']);
            $customer_stmt->bindParam(':password', $customer_password);
            
            if ($customer_stmt->execute()) {
                echo "✅ Created customer: " . $customer['name'] . "<br>";
            } else {
                echo "❌ Failed to create customer: " . $customer['name'] . "<br>";
            }
        } else {
            echo "ℹ️ Customer already exists: " . $customer['name'] . "<br>";
        }
    }
    
    // Create sample services
    echo "<h3>6. Creating sample services...</h3>";
    $services = [
        ['name' => 'Basic Haircut', 'description' => 'Professional hair cutting and styling', 'price' => 500, 'duration' => 45, 'category' => 'Hair Care'],
        ['name' => 'Hair Wash & Blow Dry', 'description' => 'Hair washing with professional blow dry', 'price' => 300, 'duration' => 30, 'category' => 'Hair Care'],
        ['name' => 'Basic Facial', 'description' => 'Cleansing and moisturizing facial treatment', 'price' => 800, 'duration' => 60, 'category' => 'Skincare'],
        ['name' => 'Bridal Makeup', 'description' => 'Complete bridal makeup with trial', 'price' => 3500, 'duration' => 120, 'category' => 'Makeup'],
        ['name' => 'Manicure', 'description' => 'Basic nail care and polish', 'price' => 400, 'duration' => 45, 'category' => 'Nail Care'],
        ['name' => 'Pedicure', 'description' => 'Foot care and nail polish', 'price' => 500, 'duration' => 60, 'category' => 'Nail Care']
    ];
    
    foreach ($services as $service) {
        $check_service = $db->prepare("SELECT COUNT(*) as count FROM services WHERE name = :name");
        $check_service->bindParam(':name', $service['name']);
        $check_service->execute();
        
        if ($check_service->fetch()['count'] == 0) {
            $service_query = "INSERT INTO services (name, description, price, duration, category, status) 
                              VALUES (:name, :description, :price, :duration, :category, 'active')";
            $service_stmt = $db->prepare($service_query);
            
            $service_stmt->bindParam(':name', $service['name']);
            $service_stmt->bindParam(':description', $service['description']);
            $service_stmt->bindParam(':price', $service['price']);
            $service_stmt->bindParam(':duration', $service['duration']);
            $service_stmt->bindParam(':category', $service['category']);
            
            if ($service_stmt->execute()) {
                echo "✅ Created service: " . $service['name'] . "<br>";
            } else {
                echo "❌ Failed to create service: " . $service['name'] . "<br>";
            }
        } else {
            echo "ℹ️ Service already exists: " . $service['name'] . "<br>";
        }
    }
    
    echo "<h2>✅ Database fix completed successfully!</h2>";
    echo "<p><strong>Login Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> <EMAIL> / password</li>";
    echo "<li><strong>Staff:</strong> <EMAIL> / password (or any other staff email)</li>";
    echo "<li><strong>Customer:</strong> <EMAIL> / password (or any other customer email)</li>";
    echo "</ul>";
    echo "<p><a href='index.php' class='btn btn-primary'>Go to Application</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error occurred:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.btn:hover { background: #0056b3; }
</style>
