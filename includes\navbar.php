<?php
/**
 * Sidebar Navigation Component
 * Salon/Parlour Management System - Feminine Theme
 */
?>

<div class="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <a href="index.php?page=dashboard" class="brand">
            <i class="fas fa-spa"></i>
            <span>Beauty Salon</span>
        </a>
    </div>

    <!-- Sidebar Navigation -->
    <div class="sidebar-nav">
        <!-- Main Navigation -->
        <div class="nav-section">
            <div class="nav-section-title">Main Menu</div>

            <div class="nav-item">
                <a href="index.php?page=dashboard" class="nav-link <?php echo ($_GET['page'] ?? '') == 'dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=bookings" class="nav-link <?php echo ($_GET['page'] ?? '') == 'bookings' ? 'active' : ''; ?>">
                    <i class="fas fa-calendar-heart"></i>
                    <span>Appointments</span>
                </a>
            </div>
        </div>

        <!-- Beauty Services -->
        <div class="nav-section">
            <div class="nav-section-title">Beauty Services</div>

            <?php if (hasRole('admin') || hasRole('staff')): ?>
            <div class="nav-item">
                <a href="index.php?page=services" class="nav-link <?php echo ($_GET['page'] ?? '') == 'services' ? 'active' : ''; ?>">
                    <i class="fas fa-magic"></i>
                    <span>Services & Treatments</span>
                </a>
            </div>
            <?php endif; ?>

            <div class="nav-item">
                <a href="index.php?page=packages" class="nav-link <?php echo ($_GET['page'] ?? '') == 'packages' ? 'active' : ''; ?>">
                    <i class="fas fa-gift"></i>
                    <span>Beauty Packages</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=products" class="nav-link <?php echo ($_GET['page'] ?? '') == 'products' ? 'active' : ''; ?>">
                    <i class="fas fa-shopping-bag"></i>
                    <span>Beauty Products</span>
                </a>
            </div>
        </div>

        <!-- Customer Care -->
        <div class="nav-section">
            <div class="nav-section-title">Customer Care</div>

            <?php if (hasRole('admin') || hasRole('staff')): ?>
            <div class="nav-item">
                <a href="index.php?page=customers" class="nav-link <?php echo ($_GET['page'] ?? '') == 'customers' ? 'active' : ''; ?>">
                    <i class="fas fa-female"></i>
                    <span>Clients</span>
                </a>
            </div>
            <?php endif; ?>

            <div class="nav-item">
                <a href="index.php?page=loyalty" class="nav-link <?php echo ($_GET['page'] ?? '') == 'loyalty' ? 'active' : ''; ?>">
                    <i class="fas fa-crown"></i>
                    <span>Loyalty Program</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=reviews" class="nav-link <?php echo ($_GET['page'] ?? '') == 'reviews' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i>
                    <span>Reviews & Ratings</span>
                </a>
            </div>
        </div>

        <!-- Business Management -->
        <?php if (hasRole('admin')): ?>
        <div class="nav-section">
            <div class="nav-section-title">Business Management</div>

            <div class="nav-item">
                <a href="index.php?page=staff" class="nav-link <?php echo ($_GET['page'] ?? '') == 'staff' ? 'active' : ''; ?>">
                    <i class="fas fa-user-friends"></i>
                    <span>Beauty Experts</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=invoices" class="nav-link <?php echo ($_GET['page'] ?? '') == 'invoices' ? 'active' : ''; ?>">
                    <i class="fas fa-receipt"></i>
                    <span>Billing & Invoices</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=inventory" class="nav-link <?php echo ($_GET['page'] ?? '') == 'inventory' ? 'active' : ''; ?>">
                    <i class="fas fa-boxes"></i>
                    <span>Inventory</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=reports" class="nav-link <?php echo ($_GET['page'] ?? '') == 'reports' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics & Reports</span>
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Marketing & Promotions -->
        <div class="nav-section">
            <div class="nav-section-title">Marketing</div>

            <div class="nav-item">
                <a href="index.php?page=promotions" class="nav-link <?php echo ($_GET['page'] ?? '') == 'promotions' ? 'active' : ''; ?>">
                    <i class="fas fa-tags"></i>
                    <span>Offers & Discounts</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=gallery" class="nav-link <?php echo ($_GET['page'] ?? '') == 'gallery' ? 'active' : ''; ?>">
                    <i class="fas fa-images"></i>
                    <span>Beauty Gallery</span>
                </a>
            </div>

            <div class="nav-item">
                <a href="index.php?page=social" class="nav-link <?php echo ($_GET['page'] ?? '') == 'social' ? 'active' : ''; ?>">
                    <i class="fas fa-share-alt"></i>
                    <span>Social Media</span>
                </a>
            </div>
        </div>

        <!-- Settings & Support -->
        <div class="nav-section">
            <div class="nav-section-title">Settings</div>

            <div class="nav-item">
                <a href="index.php?page=profile" class="nav-link <?php echo ($_GET['page'] ?? '') == 'profile' ? 'active' : ''; ?>">
                    <i class="fas fa-user-circle"></i>
                    <span>My Profile</span>
                </a>
            </div>

            <?php if (hasRole('admin')): ?>
            <div class="nav-item">
                <a href="index.php?page=settings" class="nav-link <?php echo ($_GET['page'] ?? '') == 'settings' ? 'active' : ''; ?>">
                    <i class="fas fa-cog"></i>
                    <span>Salon Settings</span>
                </a>
            </div>
            <?php endif; ?>

            <div class="nav-item">
                <a href="index.php?page=help" class="nav-link <?php echo ($_GET['page'] ?? '') == 'help' ? 'active' : ''; ?>">
                    <i class="fas fa-question-circle"></i>
                    <span>Help & Support</span>
                </a>
            </div>
        </div>
    </div>

    <!-- User Profile Section -->
    <div class="user-profile">
        <div class="user-avatar">
            <?php echo strtoupper(substr($_SESSION['name'] ?? 'U', 0, 1)); ?>
        </div>
        <div class="user-info">
            <div class="user-name"><?php echo htmlspecialchars($_SESSION['name'] ?? 'User'); ?></div>
            <div class="user-role"><?php echo ucfirst($_SESSION['role'] ?? 'Guest'); ?></div>
        </div>
        <div class="mt-3">
            <a href="index.php?page=logout" class="nav-link" style="justify-content: center; padding: 8px;">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</div>
