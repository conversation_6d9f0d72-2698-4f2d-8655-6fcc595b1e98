<?php
/**
 * Main Entry Point
 * Salon/Parlour Management System
 */

require_once 'config/config.php';

// Simple routing
$page = $_GET['page'] ?? 'home';
$action = $_GET['action'] ?? 'index';

// Check if user is logged in for protected pages
$protected_pages = ['dashboard', 'bookings', 'services', 'staff', 'reports', 'profile'];
if (in_array($page, $protected_pages) && !isLoggedIn()) {
    redirect('index.php?page=login');
}

// Role-based access control
$admin_pages = ['staff', 'reports', 'services'];
$staff_pages = ['bookings', 'dashboard'];

if (in_array($page, $admin_pages) && !hasRole('admin')) {
    redirect('index.php?page=dashboard');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php
    // Include sidebar navigation if user is logged in
    if (isLoggedIn()) {
        include 'includes/navbar.php';
    }
    ?>

    <main class="main-content <?php echo !isLoggedIn() ? 'no-sidebar' : ''; ?>">
        <?php
        // Route to appropriate page
        switch ($page) {
            case 'login':
                include 'pages/auth/login.php';
                break;
            case 'register':
                include 'pages/auth/register.php';
                break;
            case 'logout':
                include 'pages/auth/logout.php';
                break;
            case 'dashboard':
                include 'pages/dashboard/index.php';
                break;
            case 'bookings':
                include 'pages/bookings/index.php';
                break;
            case 'booking_success':
                include 'pages/booking_success.php';
                break;
            case 'services':
                include 'pages/services/index.php';
                break;
            case 'packages':
                include 'pages/packages/index.php';
                break;
            case 'products':
                include 'pages/products/index.php';
                break;
            case 'customers':
                include 'pages/customers/index.php';
                break;
            case 'loyalty':
                include 'pages/loyalty/index.php';
                break;
            case 'reviews':
                include 'pages/reviews/index.php';
                break;
            case 'staff':
                include 'pages/staff/index.php';
                break;
            case 'invoices':
                include 'pages/invoices/index.php';
                break;
            case 'inventory':
                include 'pages/inventory/index.php';
                break;
            case 'reports':
                include 'pages/reports/index.php';
                break;
            case 'promotions':
                include 'pages/promotions/index.php';
                break;
            case 'gallery':
                include 'pages/gallery/index.php';
                break;
            case 'social':
                include 'pages/social/index.php';
                break;
            case 'settings':
                include 'pages/settings/index.php';
                break;
            case 'help':
                include 'pages/help/index.php';
                break;
            case 'profile':
                include 'pages/profile/index.php';
                break;
            case 'home':
            default:
                if (isLoggedIn()) {
                    redirect('index.php?page=dashboard');
                } else {
                    include 'pages/home.php';
                }
                break;
        }
        ?>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/script.js"></script>
</body>
</html>
