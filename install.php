<?php
/**
 * Installation Script
 * Salon/Parlour Management System
 */

$error = '';
$success = '';
$step = $_GET['step'] ?? 1;

if ($_POST && $step == 2) {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'salon_management';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';

    try {
        // Test database connection
        $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name`");
        $pdo->exec("USE `$db_name`");

        // Read and execute SQL schema
        $sql = file_get_contents('database/schema.sql');
        
        // Remove the first two lines (CREATE DATABASE and USE statements)
        $sql_lines = explode("\n", $sql);
        $sql_lines = array_slice($sql_lines, 2);
        $sql = implode("\n", $sql_lines);

        // Execute SQL
        $pdo->exec($sql);

        $success = "Database setup completed successfully! You can now use the system.";
        $step = 3;

    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - Salon Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="auth-card">
                        <div class="auth-logo">
                            <i class="fas fa-cut fa-3x text-primary mb-3"></i>
                            <h2>Salon Management System</h2>
                            <p class="text-muted">Installation Wizard</p>
                        </div>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($step == 1): ?>
                            <!-- Welcome Step -->
                            <div class="text-center">
                                <h4 class="mb-4">Welcome to Installation</h4>
                                <p class="text-muted mb-4">
                                    This wizard will help you set up your Salon Management System. 
                                    Please ensure you have the following ready:
                                </p>
                                
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                MySQL Database Server
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                PHP 8.0 or higher
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Web Server (Apache/Nginx)
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Database credentials
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Write permissions
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                PDO MySQL extension
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <a href="install.php?step=2" class="btn btn-primary btn-lg">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        Start Installation
                                    </a>
                                </div>
                            </div>

                        <?php elseif ($step == 2): ?>
                            <!-- Database Configuration -->
                            <h4 class="mb-4 text-center">Database Configuration</h4>
                            
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="db_host" class="form-label">Database Host</label>
                                        <input type="text" class="form-control" id="db_host" name="db_host" 
                                               value="localhost" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="db_name" class="form-label">Database Name</label>
                                        <input type="text" class="form-control" id="db_name" name="db_name" 
                                               value="salon_management" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="db_user" class="form-label">Database Username</label>
                                        <input type="text" class="form-control" id="db_user" name="db_user" 
                                               value="root" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="db_pass" class="form-label">Database Password</label>
                                        <input type="password" class="form-control" id="db_pass" name="db_pass">
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> The database will be created automatically if it doesn't exist.
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="install.php?step=1" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Back
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-database me-1"></i>
                                        Install Database
                                    </button>
                                </div>
                            </form>

                        <?php elseif ($step == 3): ?>
                            <!-- Installation Complete -->
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-4x text-success mb-4"></i>
                                <h4 class="mb-4">Installation Complete!</h4>
                                
                                <div class="alert alert-success text-start">
                                    <h6><i class="fas fa-user-shield me-2"></i>Default Admin Account:</h6>
                                    <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                                    <p class="mb-0"><strong>Password:</strong> password</p>
                                </div>

                                <div class="alert alert-warning text-start">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Security Notice:</h6>
                                    <p class="mb-1">1. Delete this install.php file for security</p>
                                    <p class="mb-1">2. Change the default admin password</p>
                                    <p class="mb-0">3. Update database credentials in config/config.php</p>
                                </div>

                                <div class="mt-4">
                                    <a href="index.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-home me-2"></i>
                                        Go to Application
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
