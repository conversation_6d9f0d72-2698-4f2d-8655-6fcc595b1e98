<?php
/**
 * Login Page
 * Salon/Parlour Management System
 */

require_once 'classes/User.php';

$error_message = '';
$success_message = '';

if ($_POST) {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);

    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($email) || empty($password)) {
        $error_message = "Please fill in all fields.";
    } else {
        if ($user->login($email, $password)) {
            // Redirect based on role
            switch ($user->role) {
                case 'admin':
                case 'staff':
                    redirect('index.php?page=dashboard');
                    break;
                case 'customer':
                    redirect('index.php?page=bookings');
                    break;
                default:
                    redirect('index.php?page=dashboard');
            }
        } else {
            $error_message = "Invalid email or password.";
        }
    }
}
?>

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="auth-card">
                    <div class="auth-logo">
                        <i class="fas fa-spa"></i>
                        <h2>Beauty Salon</h2>
                        <p>Welcome back, beautiful! ✨</p>
                    </div>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please provide a valid email address.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="invalid-feedback">
                                Please provide your password.
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sparkles me-2"></i>
                            Enter Beauty Portal
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="#" class="text-decoration-none">Forgot your password?</a>
                        </p>
                        <p class="text-muted">
                            Don't have an account? 
                            <a href="index.php?page=register" class="text-decoration-none">Sign up here</a>
                        </p>
                    </div>

                    <div class="mt-4 pt-3 border-top">
                        <h6 class="text-center text-muted mb-3">Demo Accounts</h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">
                                    <strong>Admin:</strong><br>
                                    <EMAIL><br>
                                    password
                                </small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">
                                    <strong>Staff:</strong><br>
                                    <EMAIL><br>
                                    password
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
