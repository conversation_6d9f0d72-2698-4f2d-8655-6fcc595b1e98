<?php
/**
 * Booking Success Page
 * Salon/Parlour Management System
 */

require_once 'classes/Booking.php';
require_once 'classes/User.php';
require_once 'classes/Service.php';

$database = new Database();
$db = $database->getConnection();

$booking = new Booking($db);
$user = new User($db);
$service = new Service($db);

$booking_id = $_GET['booking_id'] ?? 0;

if ($booking_id) {
    $booking->id = $booking_id;
    $booking_data = $booking->readOne();
    
    if (!$booking_data) {
        redirect('index.php?page=bookings');
    }
} else {
    redirect('index.php?page=bookings');
}
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-check-circle"></i>
        Booking Confirmed
    </h1>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="card success-card">
                <div class="card-body text-center">
                    <div class="success-icon">
                        <i class="fas fa-check-circle fa-5x text-success"></i>
                    </div>
                    <h2 class="mt-4 mb-3">Booking Confirmed Successfully! 🎉</h2>
                    <p class="lead">Your appointment has been scheduled and confirmed. We're excited to pamper you!</p>
                    
                    <div class="booking-reference">
                        <h5>Booking Reference: <span class="text-primary">#<?php echo str_pad($booking_data['id'], 6, '0', STR_PAD_LEFT); ?></span></h5>
                    </div>
                </div>
            </div>

            <!-- Booking Details -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        Appointment Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <strong>Date:</strong> <?php echo formatDate($booking_data['booking_date']); ?>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock text-primary me-2"></i>
                                <strong>Time:</strong> <?php echo date('h:i A', strtotime($booking_data['booking_time'])); ?>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-user text-primary me-2"></i>
                                <strong>Customer:</strong> <?php echo htmlspecialchars($booking_data['customer_name']); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <?php if ($booking_data['staff_name']): ?>
                            <div class="detail-item">
                                <i class="fas fa-user-tie text-primary me-2"></i>
                                <strong>Beauty Expert:</strong> <?php echo htmlspecialchars($booking_data['staff_name']); ?>
                            </div>
                            <?php endif; ?>
                            <div class="detail-item">
                                <i class="fas fa-rupee-sign text-primary me-2"></i>
                                <strong>Total Amount:</strong> <?php echo formatCurrency($booking_data['total_amount']); ?>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                <strong>Status:</strong> 
                                <span class="badge bg-warning">Pending Confirmation</span>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($booking_data['notes']): ?>
                    <div class="mt-3">
                        <strong>Special Notes:</strong>
                        <p class="text-muted"><?php echo htmlspecialchars($booking_data['notes']); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- What's Next -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-check me-2"></i>
                        What Happens Next?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item completed">
                            <div class="timeline-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Booking Submitted</h6>
                                <p>Your appointment request has been received and is being processed.</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item pending">
                            <div class="timeline-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Confirmation Call</h6>
                                <p>Our team will call you within 2 hours to confirm your appointment details.</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item pending">
                            <div class="timeline-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Reminder</h6>
                                <p>You'll receive a reminder SMS 24 hours before your appointment.</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item pending">
                            <div class="timeline-icon">
                                <i class="fas fa-spa"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>Enjoy Your Service</h6>
                                <p>Arrive 10 minutes early and enjoy your beauty treatment!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <div class="d-flex flex-wrap justify-content-center gap-3">
                        <a href="index.php?page=bookings" class="btn btn-primary">
                            <i class="fas fa-calendar me-1"></i>
                            View All Bookings
                        </a>
                        <a href="index.php?page=services" class="btn btn-outline-primary">
                            <i class="fas fa-spa me-1"></i>
                            Browse Services
                        </a>
                        <a href="index.php?page=dashboard" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-1"></i>
                            Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        Need Help?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="contact-item">
                                <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                                <h6>Call Us</h6>
                                <p>+91 98765 43210</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="contact-item">
                                <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                <h6>Email Us</h6>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                                <h6>Visit Us</h6>
                                <p>123 Beauty Street, City</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.success-card {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: none;
    border-radius: 20px;
}

.success-icon {
    margin-bottom: 20px;
}

.booking-reference {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 15px;
    margin-top: 20px;
}

.detail-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-icon {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
}

.timeline-item.completed .timeline-icon {
    background: var(--success-color);
}

.timeline-item.pending .timeline-icon {
    background: #6c757d;
}

.timeline-content h6 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.timeline-content p {
    color: #666;
    margin: 0;
    font-size: 14px;
}

.contact-item {
    padding: 20px;
    border-radius: 15px;
    background: rgba(233, 30, 99, 0.05);
    margin-bottom: 20px;
}

.contact-item h6 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.contact-item p {
    color: #666;
    margin: 0;
}
</style>
