<?php
/**
 * Bookings Management Page
 * Salon/Parlour Management System
 */

require_once 'classes/Booking.php';
require_once 'classes/Service.php';
require_once 'classes/User.php';

$database = new Database();
$db = $database->getConnection();

$booking = new Booking($db);
$service = new Service($db);
$user = new User($db);

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle actions
switch ($action) {
    case 'create':
        if ($_POST) {
            $booking->customer_id = $_POST['customer_id'];
            $booking->staff_id = $_POST['staff_id'] ?: null;
            $booking->booking_date = $_POST['booking_date'];
            $booking->booking_time = $_POST['booking_time'];
            $booking->total_amount = $_POST['total_amount'];
            $booking->notes = $_POST['notes'];
            $booking->created_by = $_SESSION['user_id'];

            // Validate time slot
            if (!$booking->isTimeSlotAvailable($booking->booking_date, $booking->booking_time, $booking->staff_id)) {
                $error = "Selected time slot is not available.";
            } else {
                if ($booking->create()) {
                    // Add selected services
                    $selected_services = $_POST['services'] ?? [];
                    $services_data = [];
                    
                    foreach ($selected_services as $service_id) {
                        // Get service price
                        $svc = new Service($db);
                        $svc->id = $service_id;
                        $svc->readOne();
                        
                        $services_data[] = [
                            'id' => $service_id,
                            'price' => $svc->price
                        ];
                    }
                    
                    $booking->addServices($services_data);
                    // Redirect to booking success page
                    redirect('index.php?page=booking_success&booking_id=' . $booking->id);
                } else {
                    $error = "Failed to create booking.";
                }
            }
        }
        break;

    case 'edit':
        $booking_id = $_GET['id'] ?? 0;
        $booking->id = $booking_id;
        
        if ($_POST) {
            $booking->customer_id = $_POST['customer_id'];
            $booking->staff_id = $_POST['staff_id'] ?: null;
            $booking->booking_date = $_POST['booking_date'];
            $booking->booking_time = $_POST['booking_time'];
            $booking->status = $_POST['status'];
            $booking->total_amount = $_POST['total_amount'];
            $booking->notes = $_POST['notes'];

            if ($booking->update()) {
                $message = "Booking updated successfully!";
                $action = 'list';
            } else {
                $error = "Failed to update booking.";
            }
        } else {
            $booking->readOne();
        }
        break;

    case 'view':
        $booking_id = $_GET['id'] ?? 0;
        $booking->id = $booking_id;
        $booking_data = $booking->readOne();
        $booking_services = $booking->getServices();
        break;

    case 'delete':
        $booking_id = $_GET['id'] ?? 0;
        $booking->id = $booking_id;
        
        if ($booking->delete()) {
            $message = "Booking deleted successfully!";
        } else {
            $error = "Failed to delete booking.";
        }
        $action = 'list';
        break;

    case 'status':
        $booking_id = $_GET['id'] ?? 0;
        $status = $_GET['status'] ?? '';
        $booking->id = $booking_id;
        
        if ($booking->updateStatus($status)) {
            $message = "Booking status updated successfully!";
        } else {
            $error = "Failed to update booking status.";
        }
        $action = 'list';
        break;
}

// Get data for list view
if ($action == 'list') {
    $filter_date = $_GET['date'] ?? '';
    $filter_status = $_GET['status'] ?? '';
    
    if ($filter_date) {
        $bookings_result = $booking->readByDate($filter_date);
    } elseif (hasRole('customer')) {
        $bookings_result = $booking->readByCustomer($_SESSION['user_id']);
    } else {
        $bookings_result = $booking->readAll();
    }
    $bookings = $bookings_result->fetchAll();
    
    // Filter by status if specified
    if ($filter_status) {
        $bookings = array_filter($bookings, function($b) use ($filter_status) {
            return $b['status'] == $filter_status;
        });
    }
}

// Get data for forms
if ($action == 'create' || $action == 'edit') {
    $services_result = $service->readActive();
    $services = $services_result->fetchAll();
    
    $customers_result = $user->readByRole('customer');
    $customers = $customers_result->fetchAll();
    
    $staff_result = $user->readByRole('staff');
    $staff = $staff_result->fetchAll();
}
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Bookings Management
                </h1>
                <?php if ($action == 'list'): ?>
                <a href="index.php?page=bookings&action=create" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    New Booking
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- Bookings List -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <h5 class="mb-0">All Bookings</h5>
                    </div>
                    <div class="col-md-8">
                        <form method="GET" class="row g-2">
                            <input type="hidden" name="page" value="bookings">
                            <div class="col-md-4">
                                <input type="date" class="form-control" name="date" 
                                       value="<?php echo htmlspecialchars($filter_date); ?>">
                            </div>
                            <div class="col-md-4">
                                <select class="form-control" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $filter_status == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="confirmed" <?php echo $filter_status == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                    <option value="completed" <?php echo $filter_status == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo $filter_status == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="index.php?page=bookings" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($bookings)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                        <p>No bookings found.</p>
                        <a href="index.php?page=bookings&action=create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Create First Booking
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Date & Time</th>
                                    <th>Staff</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($bookings as $bkg): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($bkg['customer_name']); ?></strong>
                                        <?php if ($bkg['customer_phone']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($bkg['customer_phone']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo formatDate($bkg['booking_date']); ?><br>
                                        <small class="text-muted"><?php echo date('h:i A', strtotime($bkg['booking_time'])); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($bkg['staff_name'] ?? 'Not Assigned'); ?></td>
                                    <td><?php echo formatCurrency($bkg['total_amount']); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $bkg['status']; ?>">
                                            <?php echo ucfirst($bkg['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="index.php?page=bookings&action=view&id=<?php echo $bkg['id']; ?>" 
                                               class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (hasRole('admin') || hasRole('staff')): ?>
                                            <a href="index.php?page=bookings&action=edit&id=<?php echo $bkg['id']; ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($bkg['status'] == 'pending'): ?>
                                            <a href="index.php?page=bookings&action=status&id=<?php echo $bkg['id']; ?>&status=confirmed" 
                                               class="btn btn-outline-success">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <?php endif; ?>
                                            <a href="index.php?page=bookings&action=delete&id=<?php echo $bkg['id']; ?>" 
                                               class="btn btn-outline-danger btn-delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif ($action == 'view'): ?>
        <!-- Booking Details -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Booking Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Customer Information</h6>
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($booking_data['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($booking_data['customer_email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($booking_data['customer_phone']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Booking Information</h6>
                                <p><strong>Date:</strong> <?php echo formatDate($booking_data['booking_date']); ?></p>
                                <p><strong>Time:</strong> <?php echo date('h:i A', strtotime($booking_data['booking_time'])); ?></p>
                                <p><strong>Staff:</strong> <?php echo htmlspecialchars($booking_data['staff_name'] ?? 'Not Assigned'); ?></p>
                                <p><strong>Status:</strong> 
                                    <span class="badge badge-<?php echo $booking_data['status']; ?>">
                                        <?php echo ucfirst($booking_data['status']); ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                        
                        <?php if ($booking_data['notes']): ?>
                        <div class="mt-3">
                            <h6>Notes</h6>
                            <p><?php echo nl2br(htmlspecialchars($booking_data['notes'])); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Services</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($booking_services)): ?>
                            <p class="text-muted">No services selected.</p>
                        <?php else: ?>
                            <?php $total = 0; ?>
                            <?php foreach ($booking_services as $svc): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span><?php echo htmlspecialchars($svc['service_name']); ?></span>
                                    <span><?php echo formatCurrency($svc['price']); ?></span>
                                </div>
                                <?php $total += $svc['price']; ?>
                            <?php endforeach; ?>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total:</strong>
                                <strong><?php echo formatCurrency($total); ?></strong>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="index.php?page=bookings" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List
                    </a>
                    <?php if (hasRole('admin') || hasRole('staff')): ?>
                    <a href="index.php?page=bookings&action=edit&id=<?php echo $booking_data['id']; ?>" 
                       class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>
                        Edit
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php elseif ($action == 'create' || $action == 'edit'): ?>
        <!-- Booking Form -->
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <?php echo $action == 'create' ? 'Create New Booking' : 'Edit Booking'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customer_id" class="form-label">Customer *</label>
                                    <?php if (hasRole('customer')): ?>
                                        <input type="hidden" name="customer_id" value="<?php echo $_SESSION['user_id']; ?>">
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($_SESSION['name']); ?>" readonly>
                                    <?php else: ?>
                                        <select class="form-control" id="customer_id" name="customer_id" required>
                                            <option value="">Select Customer</option>
                                            <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>"
                                                    <?php echo ($booking->customer_id ?? '') == $customer['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($customer['name'] . ' (' . $customer['email'] . ')'); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a customer.</div>
                                    <?php endif; ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="staff_id" class="form-label">Preferred Staff</label>
                                    <select class="form-control" id="staff_id" name="staff_id">
                                        <option value="">Any Available Staff</option>
                                        <?php foreach ($staff as $staff_member): ?>
                                        <option value="<?php echo $staff_member['id']; ?>"
                                                <?php echo ($booking->staff_id ?? '') == $staff_member['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($staff_member['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="booking_date" class="form-label">Booking Date *</label>
                                    <input type="date" class="form-control future-only" id="booking_date" name="booking_date"
                                           value="<?php echo htmlspecialchars($booking->booking_date ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please select a booking date.</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="booking_time" class="form-label">Booking Time *</label>
                                    <select class="form-control" id="booking_time" name="booking_time" required>
                                        <option value="">Select Time</option>
                                        <?php
                                        $start_time = strtotime('09:00');
                                        $end_time = strtotime('18:00');
                                        $interval = 30 * 60; // 30 minutes

                                        for ($time = $start_time; $time <= $end_time; $time += $interval) {
                                            $time_str = date('H:i', $time);
                                            $time_display = date('h:i A', $time);
                                            $selected = ($booking->booking_time ?? '') == $time_str ? 'selected' : '';
                                            echo "<option value='$time_str' $selected>$time_display</option>";
                                        }
                                        ?>
                                    </select>
                                    <div class="invalid-feedback">Please select a booking time.</div>
                                </div>
                            </div>

                            <?php if ($action == 'edit'): ?>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="pending" <?php echo ($booking->status ?? '') == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="confirmed" <?php echo ($booking->status ?? '') == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                        <option value="completed" <?php echo ($booking->status ?? '') == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo ($booking->status ?? '') == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label class="form-label">Select Services *</label>
                                <div class="row">
                                    <?php
                                    $current_category = '';
                                    $booking_services_ids = [];
                                    if ($action == 'edit') {
                                        $existing_services = $booking->getServices();
                                        foreach ($existing_services as $svc) {
                                            $booking_services_ids[] = $svc['service_id'];
                                        }
                                    }
                                    ?>
                                    <?php foreach ($services as $svc): ?>
                                        <?php if ($current_category != $svc['category_name']): ?>
                                            <?php if ($current_category != ''): ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="col-md-6 mb-3">
                                                <h6 class="text-primary"><?php echo htmlspecialchars($svc['category_name'] ?? 'Other'); ?></h6>
                                            <?php $current_category = $svc['category_name']; ?>
                                        <?php endif; ?>

                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="services[]"
                                                   value="<?php echo $svc['id']; ?>" id="service_<?php echo $svc['id']; ?>"
                                                   data-price="<?php echo $svc['price']; ?>"
                                                   <?php echo in_array($svc['id'], $booking_services_ids) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="service_<?php echo $svc['id']; ?>">
                                                <?php echo htmlspecialchars($svc['name']); ?>
                                                <span class="text-muted">(<?php echo formatCurrency($svc['price']); ?> - <?php echo $svc['duration']; ?> mins)</span>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <strong>Total Amount: <span id="total-amount">₹0.00</span></strong>
                                    <input type="hidden" name="total_amount" value="<?php echo htmlspecialchars($booking->total_amount ?? '0'); ?>">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="Any special requirements or notes"><?php echo htmlspecialchars($booking->notes ?? ''); ?></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="index.php?page=bookings" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to List
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    <?php echo $action == 'create' ? 'Create Booking' : 'Update Booking'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Calculate total when services are selected
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal(); // Calculate initial total
});
</script>
