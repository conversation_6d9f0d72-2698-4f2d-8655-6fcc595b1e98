<?php
/**
 * Customers Management Page
 * Salon/Parlour Management System
 */

require_once 'classes/User.php';
require_once 'classes/Booking.php';

// Check admin/staff access
if (!hasRole('admin') && !hasRole('staff')) {
    redirect('index.php?page=dashboard');
}

$database = new Database();
$db = $database->getConnection();

$user = new User($db);
$booking = new Booking($db);

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle actions
switch ($action) {
    case 'view':
        $customer_id = $_GET['id'] ?? 0;
        $user->id = $customer_id;
        $customer_data = $user->readOne();
        
        // Get customer bookings
        $bookings_result = $booking->readByCustomer($customer_id);
        $customer_bookings = $bookings_result->fetchAll();
        
        // Calculate customer stats
        $total_spent = array_sum(array_column($customer_bookings, 'total_amount'));
        $total_visits = count(array_filter($customer_bookings, function($b) { return $b['status'] == 'completed'; }));
        break;
}

// Get data for list view
if ($action == 'list') {
    $search = $_GET['search'] ?? '';
    $customers_result = $user->readByRole('customer');
    $customers = $customers_result->fetchAll();
    
    if ($search) {
        $customers = array_filter($customers, function($customer) use ($search) {
            return stripos($customer['name'], $search) !== false || 
                   stripos($customer['email'], $search) !== false ||
                   stripos($customer['phone'], $search) !== false;
        });
    }
}
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-female"></i>
        Beautiful Clients
    </h1>
    <div class="header-actions">
        <?php if ($action == 'list'): ?>
        <div class="search-box me-3">
            <form method="GET" class="d-flex">
                <input type="hidden" name="page" value="customers">
                <input type="text" class="form-control" name="search" 
                       placeholder="Search clients..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- Customers List -->
        <div class="row">
            <?php if (empty($customers)): ?>
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-female fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">No clients found</h4>
                            <p class="text-muted">Your beautiful clients will appear here once they register.</p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($customers as $customer): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card customer-card h-100">
                        <div class="card-body">
                            <div class="customer-avatar">
                                <?php echo strtoupper(substr($customer['name'], 0, 1)); ?>
                            </div>
                            
                            <div class="customer-info text-center">
                                <h5 class="customer-name"><?php echo htmlspecialchars($customer['name']); ?></h5>
                                <p class="customer-email"><?php echo htmlspecialchars($customer['email']); ?></p>
                                <?php if ($customer['phone']): ?>
                                <p class="customer-phone">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($customer['phone']); ?>
                                </p>
                                <?php endif; ?>
                                
                                <div class="customer-status">
                                    <span class="badge bg-<?php echo $customer['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($customer['status']); ?>
                                    </span>
                                </div>
                                
                                <div class="customer-since mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        Client since <?php echo formatDate($customer['created_at']); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-grid gap-2">
                                <a href="index.php?page=customers&action=view&id=<?php echo $customer['id']; ?>" 
                                   class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>
                                    View Profile
                                </a>
                                <a href="index.php?page=bookings&action=create&customer_id=<?php echo $customer['id']; ?>" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    Book Appointment
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

    <?php elseif ($action == 'view'): ?>
        <!-- Customer Profile -->
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="customer-avatar-large mb-3">
                            <?php echo strtoupper(substr($customer_data['name'], 0, 1)); ?>
                        </div>
                        
                        <h4><?php echo htmlspecialchars($customer_data['name']); ?></h4>
                        <p class="text-muted"><?php echo htmlspecialchars($customer_data['email']); ?></p>
                        
                        <?php if ($customer_data['phone']): ?>
                        <p class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <?php echo htmlspecialchars($customer_data['phone']); ?>
                        </p>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <span class="badge bg-<?php echo $customer_data['status'] == 'active' ? 'success' : 'secondary'; ?> fs-6">
                                <?php echo ucfirst($customer_data['status']); ?>
                            </span>
                        </div>
                        
                        <p class="text-muted small">
                            <i class="fas fa-calendar me-1"></i>
                            Client since <?php echo formatDate($customer_data['created_at']); ?>
                        </p>
                    </div>
                </div>
                
                <!-- Customer Stats -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Client Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $total_visits; ?></div>
                            <div class="stat-label">Total Visits</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo formatCurrency($total_spent); ?></div>
                            <div class="stat-label">Total Spent</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo count($customer_bookings); ?></div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8">
                <!-- Booking History -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Appointment History
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($customer_bookings)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No appointments found for this client.</p>
                                <a href="index.php?page=bookings&action=create&customer_id=<?php echo $customer_data['id']; ?>" 
                                   class="btn btn-primary">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    Book First Appointment
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date & Time</th>
                                            <th>Services</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($customer_bookings as $booking): ?>
                                        <tr>
                                            <td>
                                                <?php echo formatDate($booking['booking_date']); ?><br>
                                                <small class="text-muted"><?php echo date('h:i A', strtotime($booking['booking_time'])); ?></small>
                                            </td>
                                            <td>
                                                <small class="text-muted">Multiple services</small>
                                            </td>
                                            <td><?php echo formatCurrency($booking['total_amount']); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo $booking['status']; ?>">
                                                    <?php echo ucfirst($booking['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="index.php?page=bookings&action=view&id=<?php echo $booking['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="index.php?page=bookings&action=create&customer_id=<?php echo $customer_data['id']; ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-calendar-plus me-1"></i>
                                New Appointment
                            </a>
                            <a href="index.php?page=customers" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Clients
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.customer-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.1);
}

.customer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.2);
}

.customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--quaternary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
    margin: 0 auto 15px;
}

.customer-avatar-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--quaternary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 36px;
    font-weight: bold;
    margin: 0 auto;
}

.customer-name {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 5px;
}

.customer-email {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
}

.customer-phone {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.stat-item {
    text-align: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

.search-box {
    min-width: 300px;
}

@media (max-width: 768px) {
    .search-box {
        min-width: 200px;
    }
}
</style>
