<?php
/**
 * Dashboard Page
 * Salon/Parlour Management System
 */

require_once 'classes/User.php';

$database = new Database();
$db = $database->getConnection();

// Get dashboard statistics
$stats = [];

// Total customers
$query = "SELECT COUNT(*) as count FROM users WHERE role = 'customer'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['customers'] = $stmt->fetch()['count'];

// Total bookings today
$query = "SELECT COUNT(*) as count FROM bookings WHERE DATE(booking_date) = CURDATE()";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['today_bookings'] = $stmt->fetch()['count'];

// Total revenue this month
$query = "SELECT COALESCE(SUM(total_amount), 0) as revenue 
          FROM invoices 
          WHERE MONTH(created_at) = MONTH(CURDATE()) 
          AND YEAR(created_at) = YEAR(CURDATE())
          AND payment_status = 'paid'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['monthly_revenue'] = $stmt->fetch()['revenue'];

// Pending bookings
$query = "SELECT COUNT(*) as count FROM bookings WHERE status = 'pending'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['pending_bookings'] = $stmt->fetch()['count'];

// Recent bookings
$query = "SELECT b.*, u.name as customer_name, s.name as service_name 
          FROM bookings b 
          LEFT JOIN users u ON b.customer_id = u.id 
          LEFT JOIN booking_services bs ON b.id = bs.booking_id 
          LEFT JOIN services s ON bs.service_id = s.id 
          ORDER BY b.created_at DESC 
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_bookings = $stmt->fetchAll();
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-tachometer-alt"></i>
        Dashboard
    </h1>
    <div class="header-actions">
        <span class="text-muted">Welcome back, <?php echo htmlspecialchars($_SESSION['name']); ?>! 💖</span>
    </div>
</div>

<div class="container-fluid">

    <!-- Statistics Cards -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <i class="fas fa-female stat-icon"></i>
                <div class="stat-number"><?php echo number_format($stats['customers']); ?></div>
                <div class="stat-label">
                    <i class="fas fa-heart me-2"></i>
                    Beautiful Clients
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <i class="fas fa-calendar-heart stat-icon"></i>
                <div class="stat-number"><?php echo number_format($stats['today_bookings']); ?></div>
                <div class="stat-label">
                    <i class="fas fa-sparkles me-2"></i>
                    Today's Appointments
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <i class="fas fa-gem stat-icon"></i>
                <div class="stat-number"><?php echo formatCurrency($stats['monthly_revenue']); ?></div>
                <div class="stat-label">
                    <i class="fas fa-crown me-2"></i>
                    Monthly Revenue
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <i class="fas fa-clock stat-icon"></i>
                <div class="stat-number"><?php echo number_format($stats['pending_bookings']); ?></div>
                <div class="stat-label">
                    <i class="fas fa-hourglass-half me-2"></i>
                    Pending Bookings
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Bookings -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Recent Bookings
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_bookings)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <p>No recent bookings found.</p>
                            <a href="index.php?page=bookings&action=create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Create New Booking
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_bookings as $booking): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($booking['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($booking['service_name'] ?? 'Multiple Services'); ?></td>
                                        <td>
                                            <?php echo formatDate($booking['booking_date']); ?><br>
                                            <small class="text-muted"><?php echo date('h:i A', strtotime($booking['booking_time'])); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $booking['status']; ?>">
                                                <?php echo ucfirst($booking['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="index.php?page=bookings&action=view&id=<?php echo $booking['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="index.php?page=bookings" class="btn btn-outline-primary">
                                View All Bookings
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="index.php?page=bookings&action=create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            New Booking
                        </a>
                        
                        <?php if (hasRole('admin')): ?>
                        <a href="index.php?page=services&action=create" class="btn btn-outline-primary">
                            <i class="fas fa-concierge-bell me-2"></i>
                            Add Service
                        </a>
                        
                        <a href="index.php?page=staff&action=create" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            Add Staff
                        </a>
                        
                        <a href="index.php?page=reports" class="btn btn-outline-primary">
                            <i class="fas fa-chart-bar me-2"></i>
                            View Reports
                        </a>
                        <?php endif; ?>
                        
                        <a href="index.php?page=profile" class="btn btn-outline-secondary">
                            <i class="fas fa-user-edit me-2"></i>
                            Edit Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Today's Schedule (for staff) -->
            <?php if (hasRole('staff')): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Today's Schedule
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center text-muted">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <p class="small">Your appointments for today will appear here.</p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
