<?php
/**
 * Beauty Gallery Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'gallery';
$category = $_GET['category'] ?? 'all';
$message = '';
$error = '';

// Sample gallery data
$gallery_items = [
    [
        'id' => 1,
        'title' => 'Bridal Makeup Transformation',
        'category' => 'bridal',
        'description' => 'Stunning bridal makeup with traditional and modern elements',
        'image' => 'bridal-1.jpg',
        'before_image' => 'bridal-1-before.jpg',
        'artist' => '<PERSON>',
        'date' => '2024-01-15',
        'likes' => 45,
        'tags' => ['bridal', 'makeup', 'traditional', 'glam']
    ],
    [
        'id' => 2,
        'title' => 'Hair Color Transformation',
        'category' => 'hair',
        'description' => 'Beautiful balayage highlights with a fresh cut',
        'image' => 'hair-1.jpg',
        'before_image' => 'hair-1-before.jpg',
        'artist' => '<PERSON>',
        'date' => '2024-01-12',
        'likes' => 32,
        'tags' => ['hair', 'color', 'balayage', 'highlights']
    ],
    [
        'id' => 3,
        'title' => 'Glowing Skin Facial Results',
        'category' => 'skincare',
        'description' => 'Anti-aging facial treatment showing amazing results',
        'image' => 'facial-1.jpg',
        'before_image' => 'facial-1-before.jpg',
        'artist' => 'Jennifer Davis',
        'date' => '2024-01-10',
        'likes' => 28,
        'tags' => ['skincare', 'facial', 'antiaging', 'glow']
    ],
    [
        'id' => 4,
        'title' => 'Nail Art Masterpiece',
        'category' => 'nails',
        'description' => 'Intricate nail art design with floral patterns',
        'image' => 'nails-1.jpg',
        'before_image' => null,
        'artist' => 'Sophie Chen',
        'date' => '2024-01-08',
        'likes' => 56,
        'tags' => ['nails', 'art', 'floral', 'design']
    ],
    [
        'id' => 5,
        'title' => 'Party Makeup Look',
        'category' => 'makeup',
        'description' => 'Glamorous party makeup with bold eyes and perfect contouring',
        'image' => 'party-1.jpg',
        'before_image' => 'party-1-before.jpg',
        'artist' => 'Anna Thompson',
        'date' => '2024-01-05',
        'likes' => 41,
        'tags' => ['makeup', 'party', 'glam', 'bold']
    ],
    [
        'id' => 6,
        'title' => 'Hair Styling Elegance',
        'category' => 'hair',
        'description' => 'Elegant updo perfect for special occasions',
        'image' => 'hair-2.jpg',
        'before_image' => 'hair-2-before.jpg',
        'artist' => 'Emily Rodriguez',
        'date' => '2024-01-03',
        'likes' => 38,
        'tags' => ['hair', 'updo', 'elegant', 'formal']
    ]
];

$categories = [
    'all' => ['name' => 'All Work', 'icon' => 'fas fa-th'],
    'bridal' => ['name' => 'Bridal', 'icon' => 'fas fa-ring'],
    'makeup' => ['name' => 'Makeup', 'icon' => 'fas fa-palette'],
    'hair' => ['name' => 'Hair', 'icon' => 'fas fa-cut'],
    'skincare' => ['name' => 'Skincare', 'icon' => 'fas fa-spa'],
    'nails' => ['name' => 'Nails', 'icon' => 'fas fa-hand-sparkles']
];
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-images"></i>
        Beauty Gallery
    </h1>
    <div class="header-actions">
        <?php if (hasRole('admin') || hasRole('staff')): ?>
        <a href="index.php?page=gallery&action=upload" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i>
            Upload Photo
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <!-- Gallery Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-images fa-3x text-primary mb-3"></i>
                    <h4><?php echo count($gallery_items); ?></h4>
                    <p class="text-muted">Total Photos</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                    <h4><?php echo array_sum(array_column($gallery_items, 'likes')); ?></h4>
                    <p class="text-muted">Total Likes</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-friends fa-3x text-success mb-3"></i>
                    <h4><?php echo count(array_unique(array_column($gallery_items, 'artist'))); ?></h4>
                    <p class="text-muted">Beauty Artists</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-3x text-info mb-3"></i>
                    <h4>30</h4>
                    <p class="text-muted">Days Active</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2 justify-content-center">
                        <?php foreach ($categories as $cat_key => $cat_info): ?>
                        <a href="index.php?page=gallery&category=<?php echo $cat_key; ?>" 
                           class="btn <?php echo $category == $cat_key ? 'btn-primary' : 'btn-outline-primary'; ?> category-btn">
                            <i class="<?php echo $cat_info['icon']; ?> me-2"></i>
                            <?php echo $cat_info['name']; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gallery Grid -->
    <div class="row gallery-grid">
        <?php foreach ($gallery_items as $item): ?>
        <?php if ($category == 'all' || $item['category'] == $category): ?>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="gallery-item">
                <div class="gallery-image-container">
                    <div class="gallery-image">
                        <div class="image-placeholder">
                            <i class="fas fa-image fa-3x"></i>
                        </div>
                        <?php if ($item['before_image']): ?>
                        <div class="before-after-toggle">
                            <button class="btn btn-sm btn-light" onclick="toggleBeforeAfter(<?php echo $item['id']; ?>)">
                                <i class="fas fa-exchange-alt"></i>
                                Before/After
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="gallery-overlay">
                        <div class="overlay-content">
                            <h6><?php echo htmlspecialchars($item['title']); ?></h6>
                            <p><?php echo htmlspecialchars($item['description']); ?></p>
                            <div class="gallery-actions">
                                <button class="btn btn-light btn-sm" onclick="likePhoto(<?php echo $item['id']; ?>)">
                                    <i class="fas fa-heart"></i>
                                    <?php echo $item['likes']; ?>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="sharePhoto(<?php echo $item['id']; ?>)">
                                    <i class="fas fa-share"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="viewFullscreen(<?php echo $item['id']; ?>)">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="gallery-info">
                    <div class="gallery-meta">
                        <div class="artist-info">
                            <i class="fas fa-user-tie me-1"></i>
                            <span><?php echo htmlspecialchars($item['artist']); ?></span>
                        </div>
                        <div class="date-info">
                            <i class="fas fa-calendar me-1"></i>
                            <span><?php echo formatDate($item['date']); ?></span>
                        </div>
                    </div>
                    
                    <div class="gallery-tags">
                        <?php foreach ($item['tags'] as $tag): ?>
                        <span class="tag">#<?php echo $tag; ?></span>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="gallery-stats">
                        <span class="likes">
                            <i class="fas fa-heart text-danger me-1"></i>
                            <?php echo $item['likes']; ?> likes
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endforeach; ?>
    </div>

    <!-- Call to Action -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card cta-card">
                <div class="card-body text-center">
                    <i class="fas fa-camera fa-4x text-primary mb-4"></i>
                    <h3>Love What You See?</h3>
                    <p class="lead">Book your appointment today and let our talented artists create your perfect look!</p>
                    <div class="mt-4">
                        <a href="index.php?page=bookings&action=create" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-calendar-plus me-2"></i>
                            Book Appointment
                        </a>
                        <a href="index.php?page=services" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-list me-2"></i>
                            View Services
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.gallery-grid {
    margin: 0 -10px;
}

.gallery-grid .col-lg-4,
.gallery-grid .col-md-6 {
    padding: 0 10px;
}

.gallery-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.1);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.2);
}

.gallery-image-container {
    position: relative;
    overflow: hidden;
}

.gallery-image {
    height: 250px;
    background: linear-gradient(135deg, #F8BBD0, #E1BEE7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    position: relative;
}

.image-placeholder {
    text-align: center;
    opacity: 0.7;
}

.before-after-toggle {
    position: absolute;
    top: 10px;
    right: 10px;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(233, 30, 99, 0.9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    padding: 20px;
}

.overlay-content h6 {
    margin-bottom: 10px;
    font-weight: 600;
}

.overlay-content p {
    font-size: 14px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.gallery-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.gallery-info {
    padding: 20px;
}

.gallery-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.gallery-tags {
    margin-bottom: 15px;
}

.tag {
    display: inline-block;
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.gallery-stats {
    font-size: 14px;
    color: #666;
}

.category-btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
}

.cta-card {
    background: linear-gradient(135deg, var(--accent-color), rgba(233, 30, 99, 0.1));
    border: none;
    border-radius: 25px;
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.15);
}

@media (max-width: 768px) {
    .gallery-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .gallery-actions {
        flex-wrap: wrap;
    }
}
</style>

<script>
function toggleBeforeAfter(itemId) {
    alert('Before/After toggle (Feature coming soon)');
}

function likePhoto(itemId) {
    // Toggle like status
    alert('Photo liked! (Feature coming soon)');
}

function sharePhoto(itemId) {
    // Share photo functionality
    if (navigator.share) {
        navigator.share({
            title: 'Beautiful work from our salon!',
            text: 'Check out this amazing transformation!',
            url: window.location.href
        });
    } else {
        // Fallback to copy link
        navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
    }
}

function viewFullscreen(itemId) {
    alert('Fullscreen view (Feature coming soon)');
}
</script>
