<?php
/**
 * Help & Support Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'faq';
$message = '';
$error = '';

// Sample FAQ data
$faqs = [
    [
        'category' => 'Booking',
        'question' => 'How do I book an appointment?',
        'answer' => 'You can book an appointment by clicking on "Appointments" in the menu, then "Book Appointment". Select your desired service, date, and time, then confirm your booking.'
    ],
    [
        'category' => 'Booking',
        'question' => 'Can I cancel or reschedule my appointment?',
        'answer' => 'Yes, you can cancel or reschedule your appointment up to 24 hours before the scheduled time. Go to your booking history and click on the appointment to modify it.'
    ],
    [
        'category' => 'Services',
        'question' => 'What services do you offer?',
        'answer' => 'We offer a wide range of beauty services including hair styling, makeup, facial treatments, nail care, spa services, and bridal packages. Check our Services page for the complete list.'
    ],
    [
        'category' => 'Payment',
        'question' => 'What payment methods do you accept?',
        'answer' => 'We accept cash, credit/debit cards, UPI payments, and digital wallets. Payment can be made at the salon after your service or online during booking.'
    ],
    [
        'category' => 'Loyalty',
        'question' => 'How does the loyalty program work?',
        'answer' => 'Earn points for every rupee spent on services and products. Points can be redeemed for discounts and free services. Check the Loyalty Program page for tier benefits.'
    ],
    [
        'category' => 'Account',
        'question' => 'How do I update my profile information?',
        'answer' => 'Go to "My Profile" in the menu to update your personal information, contact details, and preferences.'
    ]
];

$contact_info = [
    'phone' => '+91 98765 43210',
    'email' => '<EMAIL>',
    'address' => '123 Beauty Street, City, State 12345',
    'hours' => 'Mon-Sat: 9 AM - 8 PM, Sun: 10 AM - 6 PM'
];
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-question-circle"></i>
        Help & Support
    </h1>
</div>

<div class="container-fluid">
    <!-- Help Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2 justify-content-center">
                        <a href="index.php?page=help&action=faq" 
                           class="btn <?php echo $action == 'faq' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-question-circle me-1"></i>
                            FAQ
                        </a>
                        <a href="index.php?page=help&action=contact" 
                           class="btn <?php echo $action == 'contact' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-phone me-1"></i>
                            Contact Us
                        </a>
                        <a href="index.php?page=help&action=tutorials" 
                           class="btn <?php echo $action == 'tutorials' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-play-circle me-1"></i>
                            Tutorials
                        </a>
                        <a href="index.php?page=help&action=feedback" 
                           class="btn <?php echo $action == 'feedback' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-comment me-1"></i>
                            Feedback
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action == 'faq'): ?>
    <!-- FAQ Section -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Frequently Asked Questions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <?php foreach ($faqs as $index => $faq): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                                <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" 
                                        type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse<?php echo $index; ?>">
                                    <span class="category-badge me-2"><?php echo $faq['category']; ?></span>
                                    <?php echo htmlspecialchars($faq['question']); ?>
                                </button>
                            </h2>
                            <div id="collapse<?php echo $index; ?>" 
                                 class="accordion-collapse collapse <?php echo $index == 0 ? 'show' : ''; ?>" 
                                 data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <?php echo htmlspecialchars($faq['answer']); ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php elseif ($action == 'contact'): ?>
    <!-- Contact Section -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Phone</h6>
                            <p><?php echo $contact_info['phone']; ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Email</h6>
                            <p><?php echo $contact_info['email']; ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Address</h6>
                            <p><?php echo $contact_info['address']; ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Business Hours</h6>
                            <p><?php echo $contact_info['hours']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        Send us a Message
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Subject</label>
                            <select class="form-control" required>
                                <option value="">Select a topic</option>
                                <option value="booking">Booking Issue</option>
                                <option value="payment">Payment Problem</option>
                                <option value="service">Service Inquiry</option>
                                <option value="technical">Technical Support</option>
                                <option value="feedback">General Feedback</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Message</label>
                            <textarea class="form-control" rows="5" placeholder="Describe your issue or question..." required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Priority</label>
                            <select class="form-control">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php elseif ($action == 'tutorials'): ?>
    <!-- Tutorials Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-play-circle me-2"></i>
                        Video Tutorials
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="tutorial-card">
                                <div class="tutorial-thumbnail">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <div class="tutorial-duration">3:45</div>
                                </div>
                                <div class="tutorial-info">
                                    <h6>How to Book an Appointment</h6>
                                    <p class="text-muted">Learn how to book your beauty appointment online</p>
                                    <button class="btn btn-primary btn-sm" onclick="playTutorial(1)">
                                        <i class="fas fa-play me-1"></i>
                                        Watch Now
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="tutorial-card">
                                <div class="tutorial-thumbnail">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <div class="tutorial-duration">2:30</div>
                                </div>
                                <div class="tutorial-info">
                                    <h6>Managing Your Profile</h6>
                                    <p class="text-muted">Update your personal information and preferences</p>
                                    <button class="btn btn-primary btn-sm" onclick="playTutorial(2)">
                                        <i class="fas fa-play me-1"></i>
                                        Watch Now
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="tutorial-card">
                                <div class="tutorial-thumbnail">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                    <div class="tutorial-duration">4:15</div>
                                </div>
                                <div class="tutorial-info">
                                    <h6>Using Loyalty Points</h6>
                                    <p class="text-muted">Earn and redeem points for amazing rewards</p>
                                    <button class="btn btn-primary btn-sm" onclick="playTutorial(3)">
                                        <i class="fas fa-play me-1"></i>
                                        Watch Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php else: ?>
    <!-- Feedback Section -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comment me-2"></i>
                        Share Your Feedback
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Overall Experience</label>
                            <div class="rating-input">
                                <input type="radio" name="rating" value="5" id="star5">
                                <label for="star5"><i class="fas fa-star"></i></label>
                                <input type="radio" name="rating" value="4" id="star4">
                                <label for="star4"><i class="fas fa-star"></i></label>
                                <input type="radio" name="rating" value="3" id="star3">
                                <label for="star3"><i class="fas fa-star"></i></label>
                                <input type="radio" name="rating" value="2" id="star2">
                                <label for="star2"><i class="fas fa-star"></i></label>
                                <input type="radio" name="rating" value="1" id="star1">
                                <label for="star1"><i class="fas fa-star"></i></label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">What did you like most?</label>
                            <textarea class="form-control" rows="3" placeholder="Tell us what you enjoyed..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">What can we improve?</label>
                            <textarea class="form-control" rows="3" placeholder="Share your suggestions..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox">
                                <label class="form-check-label">
                                    I would recommend this salon to friends and family
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-heart me-1"></i>
                            Submit Feedback
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Quick Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h5>Need Immediate Help?</h5>
                    <p class="text-muted">Our support team is here to help you!</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="tel:<?php echo $contact_info['phone']; ?>" class="btn btn-success">
                            <i class="fas fa-phone me-1"></i>
                            Call Now
                        </a>
                        <a href="mailto:<?php echo $contact_info['email']; ?>" class="btn btn-primary">
                            <i class="fas fa-envelope me-1"></i>
                            Email Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.category-badge {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 15px;
    font-size: 20px;
}

.contact-details h6 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
}

.contact-details p {
    margin: 0;
    color: #666;
}

.tutorial-card {
    border: 1px solid #eee;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.tutorial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.15);
}

.tutorial-thumbnail {
    height: 150px;
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
}

.tutorial-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
}

.tutorial-info {
    padding: 15px;
}

.tutorial-info h6 {
    color: var(--primary-color);
    margin-bottom: 8px;
}

.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 5px;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    font-size: 24px;
    color: #ddd;
    transition: color 0.2s;
}

.rating-input input:checked ~ label,
.rating-input label:hover,
.rating-input label:hover ~ label {
    color: #FFD700;
}
</style>

<script>
function playTutorial(tutorialId) {
    alert(`Playing tutorial ${tutorialId} (Feature coming soon)`);
}
</script>
