<?php
/**
 * Inventory Management Page
 * Salon/Parlour Management System
 */

// Check admin access
if (!hasRole('admin')) {
    redirect('index.php?page=dashboard');
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Sample inventory data
$inventory_items = [
    [
        'id' => 1,
        'name' => 'Professional Hair Shampoo',
        'category' => 'Hair Care',
        'brand' => 'SalonPro',
        'current_stock' => 25,
        'min_stock' => 10,
        'max_stock' => 50,
        'unit_cost' => 450,
        'selling_price' => 650,
        'supplier' => 'Beauty Supplies Co.',
        'last_restocked' => '2024-01-10',
        'expiry_date' => '2025-06-15',
        'location' => 'Storage Room A'
    ],
    [
        'id' => 2,
        'name' => 'Organic Face Mask',
        'category' => 'Skincare',
        'brand' => 'NaturalGlow',
        'current_stock' => 8,
        'min_stock' => 15,
        'max_stock' => 40,
        'unit_cost' => 280,
        'selling_price' => 420,
        'supplier' => 'Organic Beauty Ltd.',
        'last_restocked' => '2024-01-05',
        'expiry_date' => '2024-12-20',
        'location' => 'Treatment Room 1'
    ],
    [
        'id' => 3,
        'name' => 'Nail Polish Set',
        'category' => 'Nail Care',
        'brand' => 'ColorMagic',
        'current_stock' => 35,
        'min_stock' => 20,
        'max_stock' => 60,
        'unit_cost' => 150,
        'selling_price' => 250,
        'supplier' => 'Nail Art Supplies',
        'last_restocked' => '2024-01-12',
        'expiry_date' => '2026-03-10',
        'location' => 'Nail Station'
    ],
    [
        'id' => 4,
        'name' => 'Makeup Brushes Set',
        'category' => 'Tools',
        'brand' => 'ProArtist',
        'current_stock' => 12,
        'min_stock' => 8,
        'max_stock' => 25,
        'unit_cost' => 800,
        'selling_price' => 1200,
        'supplier' => 'Professional Tools Inc.',
        'last_restocked' => '2024-01-08',
        'expiry_date' => null,
        'location' => 'Makeup Station'
    ],
    [
        'id' => 5,
        'name' => 'Massage Oil',
        'category' => 'Spa',
        'brand' => 'RelaxEssence',
        'current_stock' => 6,
        'min_stock' => 12,
        'max_stock' => 30,
        'unit_cost' => 320,
        'selling_price' => 480,
        'supplier' => 'Spa Essentials',
        'last_restocked' => '2024-01-03',
        'expiry_date' => '2025-01-15',
        'location' => 'Spa Room'
    ]
];

$categories = ['All', 'Hair Care', 'Skincare', 'Nail Care', 'Tools', 'Spa'];
$filter_category = $_GET['category'] ?? 'All';
$filter_status = $_GET['status'] ?? 'all';
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-boxes"></i>
        Inventory Management
    </h1>
    <div class="header-actions">
        <a href="index.php?page=inventory&action=create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add Item
        </a>
        <a href="index.php?page=inventory&action=restock" class="btn btn-outline-primary">
            <i class="fas fa-truck me-1"></i>
            Restock
        </a>
    </div>
</div>

<div class="container-fluid">
    <!-- Inventory Summary -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-boxes fa-3x text-primary mb-3"></i>
                    <h4><?php echo count($inventory_items); ?></h4>
                    <p class="text-muted">Total Items</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h4><?php echo count(array_filter($inventory_items, function($item) { return $item['current_stock'] <= $item['min_stock']; })); ?></h4>
                    <p class="text-muted">Low Stock Items</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-3x text-danger mb-3"></i>
                    <h4><?php echo count(array_filter($inventory_items, function($item) { return $item['expiry_date'] && strtotime($item['expiry_date']) < strtotime('+30 days'); })); ?></h4>
                    <p class="text-muted">Expiring Soon</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-rupee-sign fa-3x text-success mb-3"></i>
                    <h4><?php echo formatCurrency(array_sum(array_map(function($item) { return $item['current_stock'] * $item['unit_cost']; }, $inventory_items))); ?></h4>
                    <p class="text-muted">Total Value</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex flex-wrap gap-2">
                                <?php foreach ($categories as $category): ?>
                                <a href="index.php?page=inventory&category=<?php echo $category; ?>&status=<?php echo $filter_status; ?>" 
                                   class="btn btn-sm <?php echo $filter_category == $category ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    <?php echo $category; ?>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2 justify-content-md-end">
                                <select class="form-select form-select-sm" onchange="filterByStatus(this.value)">
                                    <option value="all" <?php echo $filter_status == 'all' ? 'selected' : ''; ?>>All Items</option>
                                    <option value="low_stock" <?php echo $filter_status == 'low_stock' ? 'selected' : ''; ?>>Low Stock</option>
                                    <option value="expiring" <?php echo $filter_status == 'expiring' ? 'selected' : ''; ?>>Expiring Soon</option>
                                    <option value="out_of_stock" <?php echo $filter_status == 'out_of_stock' ? 'selected' : ''; ?>>Out of Stock</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Inventory Items
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>Stock Level</th>
                                    <th>Cost/Price</th>
                                    <th>Supplier</th>
                                    <th>Expiry</th>
                                    <th>Location</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($inventory_items as $item): ?>
                                <?php
                                $show_item = true;
                                if ($filter_category != 'All' && $item['category'] != $filter_category) $show_item = false;
                                
                                if ($filter_status == 'low_stock' && $item['current_stock'] > $item['min_stock']) $show_item = false;
                                if ($filter_status == 'expiring' && (!$item['expiry_date'] || strtotime($item['expiry_date']) >= strtotime('+30 days'))) $show_item = false;
                                if ($filter_status == 'out_of_stock' && $item['current_stock'] > 0) $show_item = false;
                                
                                if (!$show_item) continue;
                                ?>
                                <tr>
                                    <td>
                                        <div class="item-info">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($item['brand']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="category-badge"><?php echo htmlspecialchars($item['category']); ?></span>
                                    </td>
                                    <td>
                                        <div class="stock-info">
                                            <div class="stock-level">
                                                <span class="current-stock <?php echo $item['current_stock'] <= $item['min_stock'] ? 'text-danger' : 'text-success'; ?>">
                                                    <?php echo $item['current_stock']; ?>
                                                </span>
                                                <span class="text-muted">/ <?php echo $item['max_stock']; ?></span>
                                            </div>
                                            <div class="progress mt-1" style="height: 6px;">
                                                <div class="progress-bar <?php echo $item['current_stock'] <= $item['min_stock'] ? 'bg-danger' : 'bg-success'; ?>" 
                                                     style="width: <?php echo ($item['current_stock'] / $item['max_stock']) * 100; ?>%"></div>
                                            </div>
                                            <?php if ($item['current_stock'] <= $item['min_stock']): ?>
                                            <small class="text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                Low Stock
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="price-info">
                                            <div>Cost: <?php echo formatCurrency($item['unit_cost']); ?></div>
                                            <div>Price: <?php echo formatCurrency($item['selling_price']); ?></div>
                                            <small class="text-success">
                                                Margin: <?php echo number_format((($item['selling_price'] - $item['unit_cost']) / $item['unit_cost']) * 100, 1); ?>%
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <small><?php echo htmlspecialchars($item['supplier']); ?></small>
                                    </td>
                                    <td>
                                        <?php if ($item['expiry_date']): ?>
                                        <div class="expiry-info">
                                            <?php echo formatDate($item['expiry_date']); ?>
                                            <?php if (strtotime($item['expiry_date']) < strtotime('+30 days')): ?>
                                            <br><small class="text-danger">
                                                <i class="fas fa-clock me-1"></i>
                                                Expiring Soon
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">No expiry</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo htmlspecialchars($item['location']); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editItem(<?php echo $item['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="restockItem(<?php echo $item['id']; ?>)">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteItem(<?php echo $item['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.item-info h6 {
    color: var(--primary-color);
    font-weight: 600;
}

.category-badge {
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.stock-info {
    min-width: 120px;
}

.current-stock {
    font-weight: 700;
    font-size: 16px;
}

.price-info {
    font-size: 14px;
}

.expiry-info {
    font-size: 14px;
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.15);
}
</style>

<script>
function filterByStatus(status) {
    const currentCategory = '<?php echo $filter_category; ?>';
    window.location.href = `index.php?page=inventory&category=${currentCategory}&status=${status}`;
}

function editItem(itemId) {
    window.location.href = `index.php?page=inventory&action=edit&id=${itemId}`;
}

function restockItem(itemId) {
    const quantity = prompt('Enter quantity to add:');
    if (quantity && !isNaN(quantity) && quantity > 0) {
        alert(`Added ${quantity} units to inventory (Feature coming soon)`);
    }
}

function deleteItem(itemId) {
    if (confirm('Are you sure you want to delete this item?')) {
        alert('Item deleted (Feature coming soon)');
    }
}
</script>
