<?php
/**
 * PDF Invoice Generator
 * Salon/Parlour Management System
 */

require_once '../../config/config.php';
require_once '../../classes/Invoice.php';

// Check if user is logged in
if (!isLoggedIn()) {
    die('Access denied');
}

$invoice_id = $_GET['id'] ?? 0;

if (!$invoice_id) {
    die('Invalid invoice ID');
}

$database = new Database();
$db = $database->getConnection();

$invoice = new Invoice($db);
$invoice->id = $invoice_id;
$invoice_data = $invoice->readOne();

if (!$invoice_data) {
    die('Invoice not found');
}

$services = $invoice->getServices();

// Set content type for PDF download
header('Content-Type: text/html; charset=utf-8');

// If download parameter is set, force download
if (isset($_GET['download'])) {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="invoice_' . $invoice->invoice_number . '.pdf"');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice <?php echo htmlspecialchars($invoice->invoice_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border: 1px solid #ddd;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #8B4B8C;
            padding-bottom: 20px;
        }
        
        .company-info h1 {
            color: #8B4B8C;
            margin: 0;
            font-size: 28px;
        }
        
        .company-info p {
            margin: 5px 0;
            color: #666;
        }
        
        .invoice-details {
            text-align: right;
        }
        
        .invoice-details h2 {
            color: #8B4B8C;
            margin: 0;
            font-size: 24px;
        }
        
        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .bill-to, .invoice-info {
            width: 48%;
        }
        
        .bill-to h3, .invoice-info h3 {
            color: #8B4B8C;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .services-table th {
            background-color: #8B4B8C;
            color: white;
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        
        .services-table td {
            padding: 12px;
            border: 1px solid #ddd;
        }
        
        .services-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 8px 12px;
            border: 1px solid #ddd;
        }
        
        .totals .total-row {
            background-color: #8B4B8C;
            color: white;
            font-weight: bold;
        }
        
        .payment-info {
            clear: both;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-paid {
            background-color: #28a745;
            color: white;
        }
        
        .status-pending {
            background-color: #ffc107;
            color: #333;
        }
        
        .status-partial {
            background-color: #17a2b8;
            color: white;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                border: none;
                box-shadow: none;
                padding: 0;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="company-info">
                <h1><?php echo APP_NAME; ?></h1>
                <p>Professional Salon & Parlour Services</p>
                <p>📍 123 Beauty Street, City, State 12345</p>
                <p>📞 +91 98765 43210 | ✉️ <EMAIL></p>
            </div>
            <div class="invoice-details">
                <h2>INVOICE</h2>
                <p><strong><?php echo htmlspecialchars($invoice->invoice_number); ?></strong></p>
            </div>
        </div>

        <!-- Invoice Meta Information -->
        <div class="invoice-meta">
            <div class="bill-to">
                <h3>Bill To:</h3>
                <p><strong><?php echo htmlspecialchars($invoice_data['customer_name']); ?></strong></p>
                <p><?php echo htmlspecialchars($invoice_data['customer_email']); ?></p>
                <?php if ($invoice_data['customer_phone']): ?>
                <p><?php echo htmlspecialchars($invoice_data['customer_phone']); ?></p>
                <?php endif; ?>
            </div>
            
            <div class="invoice-info">
                <h3>Invoice Details:</h3>
                <p><strong>Invoice Date:</strong> <?php echo formatDate($invoice->created_at); ?></p>
                <p><strong>Service Date:</strong> <?php echo formatDate($invoice_data['booking_date']); ?></p>
                <p><strong>Service Time:</strong> <?php echo date('h:i A', strtotime($invoice_data['booking_time'])); ?></p>
                <p><strong>Payment Status:</strong> 
                    <span class="status-badge status-<?php echo $invoice->payment_status; ?>">
                        <?php echo ucfirst($invoice->payment_status); ?>
                    </span>
                </p>
            </div>
        </div>

        <!-- Services Table -->
        <table class="services-table">
            <thead>
                <tr>
                    <th>Service</th>
                    <th>Description</th>
                    <th style="text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($services as $service): ?>
                <tr>
                    <td><?php echo htmlspecialchars($service['service_name']); ?></td>
                    <td><?php echo htmlspecialchars($service['description'] ?? '-'); ?></td>
                    <td style="text-align: right;"><?php echo formatCurrency($service['price']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals">
            <table>
                <tr>
                    <td>Subtotal:</td>
                    <td style="text-align: right;"><?php echo formatCurrency($invoice->subtotal); ?></td>
                </tr>
                <?php if ($invoice->discount_amount > 0): ?>
                <tr>
                    <td>Discount:</td>
                    <td style="text-align: right;">-<?php echo formatCurrency($invoice->discount_amount); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td>Tax (<?php echo $invoice->tax_rate; ?>%):</td>
                    <td style="text-align: right;"><?php echo formatCurrency($invoice->tax_amount); ?></td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total Amount:</strong></td>
                    <td style="text-align: right;"><strong><?php echo formatCurrency($invoice->total_amount); ?></strong></td>
                </tr>
            </table>
        </div>

        <!-- Payment Information -->
        <div class="payment-info">
            <h3>Payment Information:</h3>
            <p><strong>Payment Method:</strong> <?php echo ucfirst($invoice->payment_method); ?></p>
            <?php if ($invoice_data['notes']): ?>
            <p><strong>Notes:</strong> <?php echo nl2br(htmlspecialchars($invoice_data['notes'])); ?></p>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Thank you for choosing <?php echo APP_NAME; ?>!</p>
            <p>This is a computer-generated invoice and does not require a signature.</p>
            <p>Generated on <?php echo formatDateTime(date('Y-m-d H:i:s')); ?></p>
        </div>
    </div>

    <!-- Print/Download Actions (hidden in print) -->
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="background: #8B4B8C; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
            🖨️ Print Invoice
        </button>
        <a href="?id=<?php echo $invoice_id; ?>&download=1" style="background: #28a745; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 5px; display: inline-block;">
            📄 Download PDF
        </a>
        <a href="../../index.php?page=dashboard" style="background: #6c757d; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 5px; display: inline-block;">
            ← Back to Dashboard
        </a>
    </div>
</body>
</html>
