<?php
/**
 * Invoices Management Page
 * Salon/Parlour Management System
 */

require_once 'classes/Invoice.php';
require_once 'classes/Booking.php';

// Check admin/staff access
if (!hasRole('admin') && !hasRole('staff')) {
    redirect('index.php?page=dashboard');
}

$database = new Database();
$db = $database->getConnection();

$invoice = new Invoice($db);
$booking = new Booking($db);

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle actions
switch ($action) {
    case 'generate':
        $booking_id = $_GET['booking_id'] ?? 0;
        
        if ($booking_id) {
            // Check if invoice already exists
            if ($invoice->existsForBooking($booking_id)) {
                $error = "Invoice already exists for this booking.";
            } else {
                if ($invoice->generateFromBooking($booking_id)) {
                    $message = "Invoice generated successfully!";
                    // Update booking status to completed
                    $booking->id = $booking_id;
                    $booking->updateStatus('completed');
                } else {
                    $error = "Failed to generate invoice.";
                }
            }
        }
        $action = 'list';
        break;

    case 'update_payment':
        $invoice_id = $_GET['id'] ?? 0;
        $status = $_GET['status'] ?? '';
        $method = $_GET['method'] ?? '';
        
        if ($invoice_id && $status) {
            $invoice->id = $invoice_id;
            if ($invoice->updatePaymentStatus($status, $method)) {
                $message = "Payment status updated successfully!";
            } else {
                $error = "Failed to update payment status.";
            }
        }
        $action = 'list';
        break;

    case 'view':
        $invoice_id = $_GET['id'] ?? 0;
        $invoice->id = $invoice_id;
        $invoice_data = $invoice->readOne();
        $invoice_services = $invoice->getServices();
        break;
}

// Get data for list view
if ($action == 'list') {
    $invoices_result = $invoice->readAll();
    $invoices = $invoices_result->fetchAll();
    
    // Get completed bookings without invoices
    $query = "SELECT b.*, u.name as customer_name 
              FROM bookings b 
              LEFT JOIN users u ON b.customer_id = u.id 
              LEFT JOIN invoices i ON b.id = i.booking_id 
              WHERE b.status = 'completed' AND i.id IS NULL 
              ORDER BY b.booking_date DESC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $pending_invoices = $stmt->fetchAll();
}
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-file-invoice me-2"></i>
                    Invoices Management
                </h1>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- Pending Invoices -->
        <?php if (!empty($pending_invoices)): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Pending Invoice Generation (<?php echo count($pending_invoices); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Booking Date</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pending_invoices as $pending): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($pending['customer_name']); ?></td>
                                <td><?php echo formatDate($pending['booking_date']); ?></td>
                                <td><?php echo formatCurrency($pending['total_amount']); ?></td>
                                <td>
                                    <a href="index.php?page=invoices&action=generate&booking_id=<?php echo $pending['id']; ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-file-invoice me-1"></i>
                                        Generate Invoice
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- All Invoices -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">All Invoices</h5>
            </div>
            <div class="card-body">
                <?php if (empty($invoices)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>No invoices found.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Payment Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($invoices as $inv): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($inv['invoice_number']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($inv['customer_name']); ?></td>
                                    <td><?php echo formatDate($inv['created_at']); ?></td>
                                    <td><?php echo formatCurrency($inv['total_amount']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $inv['payment_status'] == 'paid' ? 'success' : 
                                                ($inv['payment_status'] == 'pending' ? 'warning' : 'info'); 
                                        ?>">
                                            <?php echo ucfirst($inv['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="index.php?page=invoices&action=view&id=<?php echo $inv['id']; ?>" 
                                               class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="pages/invoices/generate_pdf.php?id=<?php echo $inv['id']; ?>" 
                                               class="btn btn-outline-primary" target="_blank">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            <?php if ($inv['payment_status'] != 'paid'): ?>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-success dropdown-toggle" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-credit-card"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" 
                                                           href="index.php?page=invoices&action=update_payment&id=<?php echo $inv['id']; ?>&status=paid&method=cash">
                                                        Mark as Paid (Cash)
                                                    </a></li>
                                                    <li><a class="dropdown-item" 
                                                           href="index.php?page=invoices&action=update_payment&id=<?php echo $inv['id']; ?>&status=paid&method=card">
                                                        Mark as Paid (Card)
                                                    </a></li>
                                                    <li><a class="dropdown-item" 
                                                           href="index.php?page=invoices&action=update_payment&id=<?php echo $inv['id']; ?>&status=paid&method=upi">
                                                        Mark as Paid (UPI)
                                                    </a></li>
                                                </ul>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif ($action == 'view'): ?>
        <!-- Invoice Details -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Invoice Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Invoice Information</h6>
                                <p><strong>Invoice Number:</strong> <?php echo htmlspecialchars($invoice->invoice_number); ?></p>
                                <p><strong>Date:</strong> <?php echo formatDate($invoice->created_at); ?></p>
                                <p><strong>Payment Status:</strong> 
                                    <span class="badge bg-<?php 
                                        echo $invoice->payment_status == 'paid' ? 'success' : 
                                            ($invoice->payment_status == 'pending' ? 'warning' : 'info'); 
                                    ?>">
                                        <?php echo ucfirst($invoice->payment_status); ?>
                                    </span>
                                </p>
                                <p><strong>Payment Method:</strong> <?php echo ucfirst($invoice->payment_method); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Customer Information</h6>
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($invoice_data['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($invoice_data['customer_email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($invoice_data['customer_phone']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Amount Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span><?php echo formatCurrency($invoice->subtotal); ?></span>
                        </div>
                        <?php if ($invoice->discount_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount:</span>
                            <span>-<?php echo formatCurrency($invoice->discount_amount); ?></span>
                        </div>
                        <?php endif; ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (<?php echo $invoice->tax_rate; ?>%):</span>
                            <span><?php echo formatCurrency($invoice->tax_amount); ?></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Total:</strong>
                            <strong><?php echo formatCurrency($invoice->total_amount); ?></strong>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="index.php?page=invoices" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List
                    </a>
                    <a href="pages/invoices/generate_pdf.php?id=<?php echo $invoice->id; ?>" 
                       class="btn btn-primary" target="_blank">
                        <i class="fas fa-file-pdf me-1"></i>
                        View PDF
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
