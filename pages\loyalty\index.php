<?php
/**
 * Loyalty Program Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'overview';
$message = '';
$error = '';

// Sample loyalty data
$loyalty_tiers = [
    [
        'name' => 'Beauty Beginner',
        'min_points' => 0,
        'max_points' => 499,
        'benefits' => ['5% discount on services', 'Birthday month special offer', 'Priority booking'],
        'color' => '#E91E63',
        'icon' => 'fas fa-seedling'
    ],
    [
        'name' => 'Glamour Goddess',
        'min_points' => 500,
        'max_points' => 1499,
        'benefits' => ['10% discount on services', '15% off products', 'Complimentary mini facial', 'VIP customer support'],
        'color' => '#9C27B0',
        'icon' => 'fas fa-gem'
    ],
    [
        'name' => 'Beauty Queen',
        'min_points' => 1500,
        'max_points' => 2999,
        'benefits' => ['15% discount on services', '20% off products', 'Free monthly treatment', 'Exclusive event invitations'],
        'color' => '#FF9800',
        'icon' => 'fas fa-crown'
    ],
    [
        'name' => 'Royal Beauty',
        'min_points' => 3000,
        'max_points' => null,
        'benefits' => ['20% discount on services', '25% off products', 'Personal beauty consultant', 'Complimentary annual package'],
        'color' => '#FFD700',
        'icon' => 'fas fa-star'
    ]
];

// Sample customer loyalty data
$customer_loyalty = [
    ['name' => 'Sarah Johnson', 'points' => 2450, 'tier' => 'Beauty Queen', 'visits' => 24],
    ['name' => 'Emma Wilson', 'points' => 1890, 'tier' => 'Glamour Goddess', 'visits' => 18],
    ['name' => 'Lisa Brown', 'points' => 3250, 'tier' => 'Royal Beauty', 'visits' => 32],
    ['name' => 'Maria Garcia', 'points' => 750, 'tier' => 'Glamour Goddess', 'visits' => 8],
    ['name' => 'Jennifer Davis', 'points' => 320, 'tier' => 'Beauty Beginner', 'visits' => 4]
];

// Current user's loyalty info (if customer)
$user_points = 1250;
$user_tier = 'Glamour Goddess';
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-crown"></i>
        Loyalty Program
    </h1>
    <div class="header-actions">
        <?php if (hasRole('customer')): ?>
        <div class="user-points-display">
            <span class="points-label">Your Points:</span>
            <span class="points-value"><?php echo number_format($user_points); ?></span>
            <span class="tier-badge"><?php echo $user_tier; ?></span>
        </div>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <?php if (hasRole('customer')): ?>
        <!-- Customer Loyalty Dashboard -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card loyalty-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3 text-center">
                                <div class="loyalty-avatar">
                                    <i class="fas fa-crown fa-3x"></i>
                                </div>
                                <h5 class="mt-3"><?php echo $user_tier; ?></h5>
                            </div>
                            <div class="col-md-6">
                                <h6>Your Progress</h6>
                                <div class="progress-container">
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 75%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <?php echo number_format($user_points); ?> / 1,500 points to next tier
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="next-reward">
                                    <h6>Next Reward</h6>
                                    <p class="text-muted">250 points away from<br><strong>Beauty Queen</strong> tier</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Loyalty Tiers -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        Loyalty Tiers & Benefits
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($loyalty_tiers as $tier): ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="tier-card" style="border-color: <?php echo $tier['color']; ?>">
                                <div class="tier-header" style="background: <?php echo $tier['color']; ?>">
                                    <i class="<?php echo $tier['icon']; ?> fa-2x mb-2"></i>
                                    <h6><?php echo $tier['name']; ?></h6>
                                    <p class="tier-range">
                                        <?php echo number_format($tier['min_points']); ?>
                                        <?php if ($tier['max_points']): ?>
                                        - <?php echo number_format($tier['max_points']); ?>
                                        <?php else: ?>
                                        + 
                                        <?php endif; ?>
                                        points
                                    </p>
                                </div>
                                <div class="tier-benefits">
                                    <ul class="list-unstyled">
                                        <?php foreach ($tier['benefits'] as $benefit): ?>
                                        <li>
                                            <i class="fas fa-check text-success me-2"></i>
                                            <?php echo $benefit; ?>
                                        </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- How to Earn Points -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        How to Earn Points
                    </h6>
                </div>
                <div class="card-body">
                    <div class="earning-method">
                        <div class="earning-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="earning-details">
                            <h6>Book Services</h6>
                            <p>Earn 1 point for every ₹10 spent</p>
                        </div>
                        <div class="earning-points">+1 pt/₹10</div>
                    </div>
                    
                    <div class="earning-method">
                        <div class="earning-icon">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="earning-details">
                            <h6>Purchase Products</h6>
                            <p>Earn points on beauty product purchases</p>
                        </div>
                        <div class="earning-points">+1 pt/₹15</div>
                    </div>
                    
                    <div class="earning-method">
                        <div class="earning-icon">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <div class="earning-details">
                            <h6>Refer Friends</h6>
                            <p>Get bonus points for successful referrals</p>
                        </div>
                        <div class="earning-points">+100 pts</div>
                    </div>
                    
                    <div class="earning-method">
                        <div class="earning-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="earning-details">
                            <h6>Write Reviews</h6>
                            <p>Share your experience and earn points</p>
                        </div>
                        <div class="earning-points">+25 pts</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-gift me-2"></i>
                        Redeem Rewards
                    </h6>
                </div>
                <div class="card-body">
                    <div class="reward-item">
                        <div class="reward-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="reward-details">
                            <h6>10% Service Discount</h6>
                            <p>Valid for 30 days</p>
                        </div>
                        <div class="reward-cost">500 pts</div>
                    </div>
                    
                    <div class="reward-item">
                        <div class="reward-icon">
                            <i class="fas fa-spa"></i>
                        </div>
                        <div class="reward-details">
                            <h6>Free Basic Facial</h6>
                            <p>Complimentary 60-minute facial</p>
                        </div>
                        <div class="reward-cost">800 pts</div>
                    </div>
                    
                    <div class="reward-item">
                        <div class="reward-icon">
                            <i class="fas fa-cut"></i>
                        </div>
                        <div class="reward-details">
                            <h6>Free Hair Treatment</h6>
                            <p>Deep conditioning hair mask</p>
                        </div>
                        <div class="reward-cost">600 pts</div>
                    </div>
                    
                    <div class="reward-item">
                        <div class="reward-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="reward-details">
                            <h6>Beauty Product Bundle</h6>
                            <p>Curated skincare essentials</p>
                        </div>
                        <div class="reward-cost">1200 pts</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (hasRole('admin') || hasRole('staff')): ?>
    <!-- Top Loyalty Members -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        Top Loyalty Members
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Customer</th>
                                    <th>Points</th>
                                    <th>Tier</th>
                                    <th>Total Visits</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($customer_loyalty as $index => $customer): ?>
                                <tr>
                                    <td>
                                        <span class="rank-badge">#<?php echo $index + 1; ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($customer['name']); ?></td>
                                    <td>
                                        <span class="points-display"><?php echo number_format($customer['points']); ?></span>
                                    </td>
                                    <td>
                                        <span class="tier-badge-small"><?php echo $customer['tier']; ?></span>
                                    </td>
                                    <td><?php echo $customer['visits']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.loyalty-card {
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    color: white;
    border: none;
}

.loyalty-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.progress-container {
    position: relative;
}

.progress {
    height: 20px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
}

.progress-bar {
    background: linear-gradient(90deg, #FFD700, #FFA500);
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    margin-top: 8px;
    font-size: 14px;
}

.tier-card {
    border: 3px solid;
    border-radius: 20px;
    overflow: hidden;
    height: 100%;
}

.tier-header {
    color: white;
    text-align: center;
    padding: 20px;
}

.tier-range {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.tier-benefits {
    padding: 20px;
}

.tier-benefits li {
    margin-bottom: 8px;
    font-size: 14px;
}

.earning-method, .reward-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.earning-method:last-child, .reward-item:last-child {
    border-bottom: none;
}

.earning-icon, .reward-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 15px;
}

.earning-details, .reward-details {
    flex: 1;
}

.earning-details h6, .reward-details h6 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
}

.earning-details p, .reward-details p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.earning-points, .reward-cost {
    font-weight: 700;
    color: var(--primary-color);
}

.user-points-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.points-label {
    color: #666;
    font-size: 14px;
}

.points-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.tier-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.tier-badge-small {
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.rank-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
}

.points-display {
    font-weight: 600;
    color: var(--primary-color);
}
</style>
