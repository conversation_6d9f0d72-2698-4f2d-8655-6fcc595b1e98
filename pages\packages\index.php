<?php
/**
 * Beauty Packages Page
 * Salon/Parlour Management System
 */

require_once 'classes/Service.php';

$database = new Database();
$db = $database->getConnection();

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Sample packages data (in a real system, this would be from database)
$packages = [
    [
        'id' => 1,
        'name' => 'Bridal Beauty Package',
        'description' => 'Complete bridal makeover including hair, makeup, facial, and nail art',
        'services' => ['Bridal Makeup', 'Hair Styling', 'Facial Treatment', 'Manicure', 'Pedicure'],
        'original_price' => 8000,
        'package_price' => 6500,
        'duration' => 240,
        'image' => 'bridal-package.jpg',
        'popular' => true
    ],
    [
        'id' => 2,
        'name' => 'Glow & Glamour',
        'description' => 'Perfect for special occasions with facial, makeup, and hair styling',
        'services' => ['Anti-Aging Facial', 'Party Makeup', 'Hair Styling'],
        'original_price' => 4000,
        'package_price' => 3200,
        'duration' => 180,
        'image' => 'glow-package.jpg',
        'popular' => false
    ],
    [
        'id' => 3,
        'name' => 'Relaxation Retreat',
        'description' => 'Unwind with our spa treatments and massage therapy',
        'services' => ['Full Body Massage', 'Head Massage', 'Basic Facial'],
        'original_price' => 3500,
        'package_price' => 2800,
        'duration' => 150,
        'image' => 'spa-package.jpg',
        'popular' => false
    ],
    [
        'id' => 4,
        'name' => 'Hair Care Deluxe',
        'description' => 'Complete hair transformation with cut, color, and treatment',
        'services' => ['Hair Cut', 'Hair Wash & Blow Dry', 'Hair Treatment'],
        'original_price' => 2500,
        'package_price' => 2000,
        'duration' => 120,
        'image' => 'hair-package.jpg',
        'popular' => true
    ]
];
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-gift"></i>
        Beauty Packages
    </h1>
    <div class="header-actions">
        <?php if (hasRole('admin')): ?>
        <a href="index.php?page=packages&action=create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Create Package
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Packages Grid -->
    <div class="row">
        <?php foreach ($packages as $package): ?>
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 package-card">
                <?php if ($package['popular']): ?>
                <div class="package-badge">
                    <i class="fas fa-crown me-1"></i>
                    Most Popular
                </div>
                <?php endif; ?>
                
                <div class="package-image">
                    <div class="package-placeholder">
                        <i class="fas fa-spa fa-3x"></i>
                    </div>
                </div>
                
                <div class="card-body">
                    <h4 class="package-title"><?php echo htmlspecialchars($package['name']); ?></h4>
                    <p class="package-description"><?php echo htmlspecialchars($package['description']); ?></p>
                    
                    <div class="package-services mb-3">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-list me-1"></i>
                            Included Services:
                        </h6>
                        <ul class="list-unstyled">
                            <?php foreach ($package['services'] as $service): ?>
                            <li class="mb-1">
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo htmlspecialchars($service); ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <div class="package-pricing mb-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <span class="original-price"><?php echo formatCurrency($package['original_price']); ?></span>
                                <span class="package-price"><?php echo formatCurrency($package['package_price']); ?></span>
                            </div>
                            <div class="savings-badge">
                                Save <?php echo formatCurrency($package['original_price'] - $package['package_price']); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="package-duration mb-3">
                        <i class="fas fa-clock text-muted me-1"></i>
                        <span class="text-muted"><?php echo $package['duration']; ?> minutes</span>
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="bookPackage(<?php echo $package['id']; ?>)">
                            <i class="fas fa-calendar-heart me-2"></i>
                            Book This Package
                        </button>
                        <?php if (hasRole('admin')): ?>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Package Benefits Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        Why Choose Our Beauty Packages?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-percentage fa-3x text-primary mb-3"></i>
                            <h6>Save Money</h6>
                            <p class="text-muted">Up to 30% savings compared to individual services</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                            <h6>Time Efficient</h6>
                            <p class="text-muted">All services in one convenient appointment</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-user-friends fa-3x text-primary mb-3"></i>
                            <h6>Expert Care</h6>
                            <p class="text-muted">Coordinated treatment by our beauty experts</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-gift fa-3x text-primary mb-3"></i>
                            <h6>Special Perks</h6>
                            <p class="text-muted">Exclusive add-ons and complimentary services</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.package-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(233, 30, 99, 0.2);
}

.package-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.package-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
}

.package-placeholder {
    text-align: center;
    opacity: 0.7;
}

.package-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 15px;
}

.package-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.package-pricing {
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding: 15px 0;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 14px;
    margin-right: 10px;
}

.package-price {
    color: var(--primary-color);
    font-size: 24px;
    font-weight: 700;
}

.savings-badge {
    background: var(--success-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}
</style>

<script>
function bookPackage(packageId) {
    // Redirect to booking page with package pre-selected
    window.location.href = 'index.php?page=bookings&action=create&package=' + packageId;
}
</script>
