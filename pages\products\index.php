<?php
/**
 * Beauty Products Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Sample products data
$products = [
    [
        'id' => 1,
        'name' => 'Hydrating Face Serum',
        'brand' => 'GlowBeauty',
        'category' => 'Skincare',
        'price' => 1299,
        'stock' => 25,
        'description' => 'Intensive hydrating serum with hyaluronic acid',
        'image' => 'serum.jpg',
        'rating' => 4.8,
        'bestseller' => true
    ],
    [
        'id' => 2,
        'name' => 'Matte Lipstick Set',
        'brand' => 'ColorPop',
        'category' => 'Makeup',
        'price' => 899,
        'stock' => 15,
        'description' => 'Long-lasting matte lipstick in 6 beautiful shades',
        'image' => 'lipstick.jpg',
        'rating' => 4.6,
        'bestseller' => false
    ],
    [
        'id' => 3,
        'name' => 'Argan Oil Hair Mask',
        'brand' => 'HairLux',
        'category' => 'Hair Care',
        'price' => 799,
        'stock' => 30,
        'description' => 'Deep conditioning mask with pure argan oil',
        'image' => 'hair-mask.jpg',
        'rating' => 4.7,
        'bestseller' => true
    ],
    [
        'id' => 4,
        'name' => 'Vitamin C Brightening Cream',
        'brand' => 'RadiantSkin',
        'category' => 'Skincare',
        'price' => 1599,
        'stock' => 20,
        'description' => 'Brightening day cream with vitamin C and SPF 30',
        'image' => 'cream.jpg',
        'rating' => 4.9,
        'bestseller' => false
    ],
    [
        'id' => 5,
        'name' => 'Professional Makeup Brushes',
        'brand' => 'BeautyTools',
        'category' => 'Tools',
        'price' => 2499,
        'stock' => 12,
        'description' => 'Complete set of 12 professional makeup brushes',
        'image' => 'brushes.jpg',
        'rating' => 4.5,
        'bestseller' => false
    ],
    [
        'id' => 6,
        'name' => 'Nail Polish Collection',
        'brand' => 'NailArt',
        'category' => 'Nail Care',
        'price' => 699,
        'stock' => 40,
        'description' => 'Set of 8 trendy nail polish colors',
        'image' => 'nail-polish.jpg',
        'rating' => 4.4,
        'bestseller' => true
    ]
];

$categories = ['All', 'Skincare', 'Makeup', 'Hair Care', 'Nail Care', 'Tools'];
$filter_category = $_GET['category'] ?? 'All';
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-shopping-bag"></i>
        Beauty Products
    </h1>
    <div class="header-actions">
        <?php if (hasRole('admin')): ?>
        <a href="index.php?page=products&action=create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add Product
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <!-- Category Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <?php foreach ($categories as $category): ?>
                        <a href="index.php?page=products&category=<?php echo $category; ?>" 
                           class="btn <?php echo $filter_category == $category ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <?php echo $category; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="row">
        <?php foreach ($products as $product): ?>
        <?php if ($filter_category == 'All' || $product['category'] == $filter_category): ?>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 product-card">
                <?php if ($product['bestseller']): ?>
                <div class="product-badge">
                    <i class="fas fa-fire me-1"></i>
                    Bestseller
                </div>
                <?php endif; ?>
                
                <div class="product-image">
                    <div class="product-placeholder">
                        <i class="fas fa-shopping-bag fa-3x"></i>
                    </div>
                    <?php if ($product['stock'] < 10): ?>
                    <div class="stock-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Low Stock
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <div class="product-category"><?php echo htmlspecialchars($product['category']); ?></div>
                    <h5 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                    <p class="product-brand">by <?php echo htmlspecialchars($product['brand']); ?></p>
                    <p class="product-description"><?php echo htmlspecialchars($product['description']); ?></p>
                    
                    <div class="product-rating mb-2">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <i class="fas fa-star <?php echo $i <= $product['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                        <?php endfor; ?>
                        <span class="ms-2 text-muted">(<?php echo $product['rating']; ?>)</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="product-price"><?php echo formatCurrency($product['price']); ?></div>
                        <div class="product-stock">
                            <i class="fas fa-box me-1"></i>
                            <?php echo $product['stock']; ?> in stock
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="addToCart(<?php echo $product['id']; ?>)">
                            <i class="fas fa-cart-plus me-2"></i>
                            Add to Cart
                        </button>
                        <?php if (hasRole('admin')): ?>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endforeach; ?>
    </div>
    
    <!-- Product Categories Info -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Product Categories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 text-center mb-3">
                            <i class="fas fa-spa fa-3x text-primary mb-2"></i>
                            <h6>Skincare</h6>
                            <p class="text-muted small">Serums, creams, cleansers</p>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <i class="fas fa-palette fa-3x text-primary mb-2"></i>
                            <h6>Makeup</h6>
                            <p class="text-muted small">Lipsticks, foundations, eyeshadows</p>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <i class="fas fa-cut fa-3x text-primary mb-2"></i>
                            <h6>Hair Care</h6>
                            <p class="text-muted small">Shampoos, masks, treatments</p>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <i class="fas fa-hands fa-3x text-primary mb-2"></i>
                            <h6>Nail Care</h6>
                            <p class="text-muted small">Polishes, treatments, tools</p>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <i class="fas fa-tools fa-3x text-primary mb-2"></i>
                            <h6>Tools</h6>
                            <p class="text-muted small">Brushes, applicators, accessories</p>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <i class="fas fa-gift fa-3x text-primary mb-2"></i>
                            <h6>Gift Sets</h6>
                            <p class="text-muted small">Curated beauty collections</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.product-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(233, 30, 99, 0.15);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    z-index: 2;
}

.stock-warning {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #FFA500;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    z-index: 2;
}

.product-image {
    height: 200px;
    background: linear-gradient(135deg, #F8BBD0, #E1BEE7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    position: relative;
}

.product-placeholder {
    text-align: center;
    opacity: 0.7;
}

.product-category {
    color: var(--primary-color);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 8px;
}

.product-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 16px;
}

.product-brand {
    color: #666;
    font-size: 13px;
    font-style: italic;
    margin-bottom: 10px;
}

.product-description {
    color: #777;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 15px;
}

.product-price {
    color: var(--primary-color);
    font-size: 18px;
    font-weight: 700;
}

.product-stock {
    color: #666;
    font-size: 12px;
}

.product-rating .fa-star {
    font-size: 14px;
}
</style>

<script>
function addToCart(productId) {
    // Add to cart functionality
    alert('Product added to cart! (Feature coming soon)');
}
</script>
