<?php
/**
 * Promotions & Offers Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Sample promotions data
$promotions = [
    [
        'id' => 1,
        'title' => 'New Year Beauty Blast',
        'description' => 'Start the year with a fresh new look! Get 30% off on all beauty packages.',
        'discount_type' => 'percentage',
        'discount_value' => 30,
        'min_amount' => 2000,
        'start_date' => '2024-01-01',
        'end_date' => '2024-01-31',
        'status' => 'active',
        'usage_limit' => 100,
        'used_count' => 45,
        'applicable_services' => ['packages'],
        'code' => 'NEWYEAR30',
        'image' => 'newyear-promo.jpg'
    ],
    [
        'id' => 2,
        'title' => 'Bridal Beauty Special',
        'description' => 'Perfect for your special day! Complete bridal package with complimentary trial.',
        'discount_type' => 'fixed',
        'discount_value' => 1500,
        'min_amount' => 5000,
        'start_date' => '2024-01-15',
        'end_date' => '2024-03-31',
        'status' => 'active',
        'usage_limit' => 50,
        'used_count' => 12,
        'applicable_services' => ['bridal'],
        'code' => 'BRIDE2024',
        'image' => 'bridal-promo.jpg'
    ],
    [
        'id' => 3,
        'title' => 'Student Discount',
        'description' => 'Special discount for students on all services. Valid with student ID.',
        'discount_type' => 'percentage',
        'discount_value' => 15,
        'min_amount' => 500,
        'start_date' => '2024-01-01',
        'end_date' => '2024-12-31',
        'status' => 'active',
        'usage_limit' => null,
        'used_count' => 78,
        'applicable_services' => ['all'],
        'code' => 'STUDENT15',
        'image' => 'student-promo.jpg'
    ],
    [
        'id' => 4,
        'title' => 'Refer a Friend',
        'description' => 'Refer a friend and both get 20% off on your next visit!',
        'discount_type' => 'percentage',
        'discount_value' => 20,
        'min_amount' => 1000,
        'start_date' => '2024-01-01',
        'end_date' => '2024-06-30',
        'status' => 'active',
        'usage_limit' => 200,
        'used_count' => 89,
        'applicable_services' => ['all'],
        'code' => 'REFER20',
        'image' => 'referral-promo.jpg'
    ],
    [
        'id' => 5,
        'title' => 'Weekend Spa Special',
        'description' => 'Relax and rejuvenate with our weekend spa packages at special prices.',
        'discount_type' => 'percentage',
        'discount_value' => 25,
        'min_amount' => 1500,
        'start_date' => '2024-01-01',
        'end_date' => '2024-02-29',
        'status' => 'expired',
        'usage_limit' => 75,
        'used_count' => 75,
        'applicable_services' => ['spa'],
        'code' => 'WEEKEND25',
        'image' => 'spa-promo.jpg'
    ]
];

$filter_status = $_GET['status'] ?? 'active';
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-tags"></i>
        Offers & Discounts
    </h1>
    <div class="header-actions">
        <?php if (hasRole('admin')): ?>
        <a href="index.php?page=promotions&action=create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Create Offer
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <!-- Promotion Stats -->
    <?php if (hasRole('admin')): ?>
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-tags fa-3x text-primary mb-3"></i>
                    <h4><?php echo count(array_filter($promotions, function($p) { return $p['status'] == 'active'; })); ?></h4>
                    <p class="text-muted">Active Offers</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x text-success mb-3"></i>
                    <h4><?php echo array_sum(array_column($promotions, 'used_count')); ?></h4>
                    <p class="text-muted">Total Usage</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-3x text-warning mb-3"></i>
                    <h4>22%</h4>
                    <p class="text-muted">Avg. Discount</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-rupee-sign fa-3x text-info mb-3"></i>
                    <h4>₹45,000</h4>
                    <p class="text-muted">Savings Given</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="index.php?page=promotions&status=active" 
                           class="btn <?php echo $filter_status == 'active' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-play me-1"></i>
                            Active Offers
                        </a>
                        <a href="index.php?page=promotions&status=upcoming" 
                           class="btn <?php echo $filter_status == 'upcoming' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-clock me-1"></i>
                            Upcoming
                        </a>
                        <a href="index.php?page=promotions&status=expired" 
                           class="btn <?php echo $filter_status == 'expired' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-history me-1"></i>
                            Expired
                        </a>
                        <a href="index.php?page=promotions&status=all" 
                           class="btn <?php echo $filter_status == 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            <i class="fas fa-list me-1"></i>
                            All Offers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotions Grid -->
    <div class="row">
        <?php foreach ($promotions as $promo): ?>
        <?php if ($filter_status == 'all' || $promo['status'] == $filter_status): ?>
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card promotion-card h-100">
                <div class="promotion-header">
                    <div class="promotion-image">
                        <div class="promotion-placeholder">
                            <i class="fas fa-gift fa-3x"></i>
                        </div>
                    </div>
                    <div class="promotion-status">
                        <span class="status-badge status-<?php echo $promo['status']; ?>">
                            <?php echo ucfirst($promo['status']); ?>
                        </span>
                    </div>
                    <?php if ($promo['discount_type'] == 'percentage'): ?>
                    <div class="discount-badge">
                        <?php echo $promo['discount_value']; ?>% OFF
                    </div>
                    <?php else: ?>
                    <div class="discount-badge">
                        ₹<?php echo $promo['discount_value']; ?> OFF
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card-body">
                    <h5 class="promotion-title"><?php echo htmlspecialchars($promo['title']); ?></h5>
                    <p class="promotion-description"><?php echo htmlspecialchars($promo['description']); ?></p>
                    
                    <div class="promotion-details">
                        <div class="detail-item">
                            <i class="fas fa-ticket-alt me-2"></i>
                            <strong>Code:</strong> <?php echo $promo['code']; ?>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-calendar me-2"></i>
                            <strong>Valid:</strong> <?php echo formatDate($promo['start_date']); ?> - <?php echo formatDate($promo['end_date']); ?>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-rupee-sign me-2"></i>
                            <strong>Min Amount:</strong> <?php echo formatCurrency($promo['min_amount']); ?>
                        </div>
                        <?php if ($promo['usage_limit']): ?>
                        <div class="detail-item">
                            <i class="fas fa-users me-2"></i>
                            <strong>Usage:</strong> <?php echo $promo['used_count']; ?> / <?php echo $promo['usage_limit']; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($promo['usage_limit']): ?>
                    <div class="usage-progress mt-3">
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo ($promo['used_count'] / $promo['usage_limit']) * 100; ?>%"></div>
                        </div>
                        <small class="text-muted">
                            <?php echo $promo['usage_limit'] - $promo['used_count']; ?> uses remaining
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="d-grid gap-2">
                        <?php if (hasRole('customer') && $promo['status'] == 'active'): ?>
                        <button class="btn btn-primary" onclick="usePromoCode('<?php echo $promo['code']; ?>')">
                            <i class="fas fa-shopping-cart me-1"></i>
                            Use This Offer
                        </button>
                        <?php endif; ?>
                        
                        <?php if (hasRole('admin')): ?>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="editPromotion(<?php echo $promo['id']; ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="viewStats(<?php echo $promo['id']; ?>)">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deletePromotion(<?php echo $promo['id']; ?>)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endforeach; ?>
    </div>

    <!-- How to Use Promotions -->
    <?php if (hasRole('customer')): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        How to Use Promotional Codes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-calendar-plus fa-3x text-primary mb-2"></i>
                            <h6>1. Book Service</h6>
                            <p class="text-muted small">Select your desired service or package</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-ticket-alt fa-3x text-primary mb-2"></i>
                            <h6>2. Enter Code</h6>
                            <p class="text-muted small">Apply the promotional code at checkout</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-percentage fa-3x text-primary mb-2"></i>
                            <h6>3. Get Discount</h6>
                            <p class="text-muted small">Enjoy instant savings on your booking</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-spa fa-3x text-primary mb-2"></i>
                            <h6>4. Enjoy Service</h6>
                            <p class="text-muted small">Relax and enjoy your beauty treatment</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.promotion-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.promotion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(233, 30, 99, 0.2);
}

.promotion-header {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.promotion-placeholder {
    text-align: center;
    opacity: 0.7;
}

.promotion-status {
    position: absolute;
    top: 15px;
    left: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: var(--success-color);
    color: white;
}

.status-expired {
    background: var(--danger-color);
    color: white;
}

.status-upcoming {
    background: var(--warning-color);
    color: white;
}

.discount-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #FFD700;
    color: var(--dark-color);
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.promotion-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 15px;
}

.promotion-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.promotion-details {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.detail-item {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.usage-progress .progress {
    height: 8px;
    border-radius: 4px;
    background: #f0f0f0;
}

.usage-progress .progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--quaternary-color));
}
</style>

<script>
function usePromoCode(code) {
    // Copy code to clipboard and redirect to booking
    navigator.clipboard.writeText(code).then(function() {
        alert(`Promo code "${code}" copied to clipboard! Redirecting to booking...`);
        window.location.href = 'index.php?page=bookings&action=create&promo=' + code;
    });
}

function editPromotion(promoId) {
    window.location.href = `index.php?page=promotions&action=edit&id=${promoId}`;
}

function viewStats(promoId) {
    alert('Promotion statistics (Feature coming soon)');
}

function deletePromotion(promoId) {
    if (confirm('Are you sure you want to delete this promotion?')) {
        alert('Promotion deleted (Feature coming soon)');
    }
}
</script>
