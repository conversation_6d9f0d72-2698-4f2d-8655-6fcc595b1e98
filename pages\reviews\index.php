<?php
/**
 * Reviews & Ratings Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Sample reviews data
$reviews = [
    [
        'id' => 1,
        'customer_name' => '<PERSON>',
        'service' => 'Bridal Makeup Package',
        'rating' => 5,
        'comment' => 'Absolutely amazing experience! The makeup artist was so talented and made me feel like a princess on my wedding day. Highly recommend!',
        'date' => '2024-01-15',
        'staff_name' => '<PERSON>',
        'verified' => true,
        'helpful_votes' => 12
    ],
    [
        'id' => 2,
        'customer_name' => '<PERSON>',
        'service' => 'Hair Styling & Treatment',
        'rating' => 4,
        'comment' => 'Great service and lovely staff. My hair looks fantastic! The only reason I\'m not giving 5 stars is the wait time was a bit longer than expected.',
        'date' => '2024-01-12',
        'staff_name' => '<PERSON>',
        'verified' => true,
        'helpful_votes' => 8
    ],
    [
        'id' => 3,
        'customer_name' => '<PERSON>',
        'service' => 'Anti-Aging Facial',
        'rating' => 5,
        'comment' => 'The facial was incredibly relaxing and my skin feels so smooth and refreshed. The aesthetician was very knowledgeable and professional.',
        'date' => '2024-01-10',
        'staff_name' => '<PERSON>',
        'verified' => true,
        'helpful_votes' => 15
    ],
    [
        'id' => 4,
        'customer_name' => 'Maria Garcia',
        'service' => 'Manicure & Pedicure',
        'rating' => 5,
        'comment' => 'Perfect nail art and such attention to detail! The salon is clean and the atmosphere is very relaxing. Will definitely come back!',
        'date' => '2024-01-08',
        'staff_name' => 'Sophie Chen',
        'verified' => true,
        'helpful_votes' => 6
    ],
    [
        'id' => 5,
        'customer_name' => 'Jennifer Davis',
        'service' => 'Full Body Massage',
        'rating' => 4,
        'comment' => 'Very relaxing massage and great ambiance. The therapist was skilled and professional. Would love to see more variety in essential oils.',
        'date' => '2024-01-05',
        'staff_name' => 'Anna Thompson',
        'verified' => false,
        'helpful_votes' => 4
    ]
];

// Calculate overall statistics
$total_reviews = count($reviews);
$average_rating = array_sum(array_column($reviews, 'rating')) / $total_reviews;
$rating_distribution = array_count_values(array_column($reviews, 'rating'));
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-star"></i>
        Reviews & Ratings
    </h1>
    <div class="header-actions">
        <?php if (hasRole('customer')): ?>
        <a href="index.php?page=reviews&action=create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Write Review
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <!-- Overall Rating Summary -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-3">
            <div class="card rating-summary-card">
                <div class="card-body text-center">
                    <div class="overall-rating">
                        <span class="rating-number"><?php echo number_format($average_rating, 1); ?></span>
                        <div class="rating-stars">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star <?php echo $i <= $average_rating ? 'text-warning' : 'text-muted'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <p class="text-muted">Based on <?php echo $total_reviews; ?> reviews</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Rating Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <?php for ($i = 5; $i >= 1; $i--): ?>
                    <div class="rating-bar-container">
                        <div class="rating-label">
                            <?php echo $i; ?> <i class="fas fa-star text-warning"></i>
                        </div>
                        <div class="rating-bar">
                            <div class="rating-fill" style="width: <?php echo isset($rating_distribution[$i]) ? ($rating_distribution[$i] / $total_reviews) * 100 : 0; ?>%"></div>
                        </div>
                        <div class="rating-count">
                            <?php echo $rating_distribution[$i] ?? 0; ?>
                        </div>
                    </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Customer Reviews
                        </h6>
                        <div class="review-filters">
                            <select class="form-select form-select-sm" onchange="filterReviews(this.value)">
                                <option value="all">All Reviews</option>
                                <option value="5">5 Stars</option>
                                <option value="4">4 Stars</option>
                                <option value="3">3 Stars</option>
                                <option value="2">2 Stars</option>
                                <option value="1">1 Star</option>
                                <option value="verified">Verified Only</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php foreach ($reviews as $review): ?>
                    <div class="review-item" data-rating="<?php echo $review['rating']; ?>" data-verified="<?php echo $review['verified'] ? 'true' : 'false'; ?>">
                        <div class="review-header">
                            <div class="reviewer-info">
                                <div class="reviewer-avatar">
                                    <?php echo strtoupper(substr($review['customer_name'], 0, 1)); ?>
                                </div>
                                <div class="reviewer-details">
                                    <h6 class="reviewer-name">
                                        <?php echo htmlspecialchars($review['customer_name']); ?>
                                        <?php if ($review['verified']): ?>
                                        <span class="verified-badge">
                                            <i class="fas fa-check-circle"></i>
                                            Verified
                                        </span>
                                        <?php endif; ?>
                                    </h6>
                                    <div class="review-meta">
                                        <span class="service-name"><?php echo htmlspecialchars($review['service']); ?></span>
                                        <span class="review-date"><?php echo formatDate($review['date']); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="review-rating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                        </div>
                        
                        <div class="review-content">
                            <p><?php echo htmlspecialchars($review['comment']); ?></p>
                        </div>
                        
                        <div class="review-footer">
                            <div class="staff-mention">
                                <i class="fas fa-user-tie me-1"></i>
                                Service by: <strong><?php echo htmlspecialchars($review['staff_name']); ?></strong>
                            </div>
                            <div class="review-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="markHelpful(<?php echo $review['id']; ?>)">
                                    <i class="fas fa-thumbs-up me-1"></i>
                                    Helpful (<?php echo $review['helpful_votes']; ?>)
                                </button>
                                <?php if (hasRole('admin')): ?>
                                <button class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-reply me-1"></i>
                                    Reply
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Statistics for Admin -->
    <?php if (hasRole('admin')): ?>
    <div class="row mt-4">
        <div class="col-md-4 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-star fa-3x text-warning mb-3"></i>
                    <h4><?php echo number_format($average_rating, 1); ?></h4>
                    <p class="text-muted">Average Rating</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-comments fa-3x text-primary mb-3"></i>
                    <h4><?php echo $total_reviews; ?></h4>
                    <p class="text-muted">Total Reviews</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h4><?php echo count(array_filter($reviews, function($r) { return $r['verified']; })); ?></h4>
                    <p class="text-muted">Verified Reviews</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.rating-summary-card {
    background: linear-gradient(135deg, var(--primary-color), var(--quaternary-color));
    color: white;
    border: none;
}

.overall-rating .rating-number {
    font-size: 4rem;
    font-weight: 700;
    display: block;
    margin-bottom: 10px;
}

.rating-stars {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.rating-bar-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.rating-label {
    width: 60px;
    font-size: 14px;
}

.rating-bar {
    flex: 1;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    margin: 0 15px;
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--quaternary-color));
    transition: width 0.3s ease;
}

.rating-count {
    width: 30px;
    text-align: right;
    font-size: 14px;
    color: #666;
}

.review-item {
    border-bottom: 1px solid #eee;
    padding: 20px 0;
}

.review-item:last-child {
    border-bottom: none;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--quaternary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 15px;
}

.reviewer-name {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-weight: 600;
}

.verified-badge {
    background: var(--success-color);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    margin-left: 8px;
}

.review-meta {
    font-size: 14px;
    color: #666;
}

.service-name {
    font-weight: 500;
    margin-right: 15px;
}

.review-content {
    margin-bottom: 15px;
    line-height: 1.6;
}

.review-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.staff-mention {
    color: #666;
}

.review-actions {
    display: flex;
    gap: 10px;
}

.review-filters {
    min-width: 150px;
}

@media (max-width: 768px) {
    .review-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .review-rating {
        margin-top: 10px;
    }
    
    .review-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>

<script>
function filterReviews(filter) {
    const reviews = document.querySelectorAll('.review-item');
    
    reviews.forEach(review => {
        let show = true;
        
        if (filter === 'verified') {
            show = review.dataset.verified === 'true';
        } else if (filter !== 'all') {
            show = review.dataset.rating === filter;
        }
        
        review.style.display = show ? 'block' : 'none';
    });
}

function markHelpful(reviewId) {
    // Mark review as helpful
    alert('Thank you for your feedback!');
}
</script>
