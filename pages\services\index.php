<?php
/**
 * Services Management Page
 * Salon/Parlour Management System
 */

require_once 'classes/Service.php';
require_once 'classes/ServiceCategory.php';

// Check admin access
if (!hasRole('admin')) {
    redirect('index.php?page=dashboard');
}

$database = new Database();
$db = $database->getConnection();

$service = new Service($db);
$category = new ServiceCategory($db);

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle actions
switch ($action) {
    case 'create':
        if ($_POST) {
            $service->name = $_POST['name'];
            $service->description = $_POST['description'];
            $service->price = $_POST['price'];
            $service->duration = $_POST['duration'];
            $service->category_id = $_POST['category_id'];

            if ($service->create()) {
                $message = "Service created successfully!";
                $action = 'list'; // Redirect to list view
            } else {
                $error = "Failed to create service.";
            }
        }
        break;

    case 'edit':
        $service_id = $_GET['id'] ?? 0;
        $service->id = $service_id;
        
        if ($_POST) {
            $service->name = $_POST['name'];
            $service->description = $_POST['description'];
            $service->price = $_POST['price'];
            $service->duration = $_POST['duration'];
            $service->category_id = $_POST['category_id'];

            if ($service->update()) {
                $message = "Service updated successfully!";
                $action = 'list';
            } else {
                $error = "Failed to update service.";
            }
        } else {
            $service->readOne();
        }
        break;

    case 'delete':
        $service_id = $_GET['id'] ?? 0;
        $service->id = $service_id;
        
        if ($service->delete()) {
            $message = "Service deleted successfully!";
        } else {
            $error = "Failed to delete service.";
        }
        $action = 'list';
        break;
}

// Get data for list view
if ($action == 'list') {
    $search = $_GET['search'] ?? '';
    if ($search) {
        $services_result = $service->search($search);
    } else {
        $services_result = $service->readAll();
    }
    $services = $services_result->fetchAll();
}

// Get categories for form
$categories_result = $category->readAll();
$categories = $categories_result->fetchAll();
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-concierge-bell me-2"></i>
                    Services Management
                </h1>
                <?php if ($action == 'list'): ?>
                <a href="index.php?page=services&action=create" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add New Service
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- Services List -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">All Services</h5>
                    </div>
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="hidden" name="page" value="services">
                            <input type="text" class="form-control me-2" name="search" 
                                   placeholder="Search services..." value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($services)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-concierge-bell fa-3x mb-3"></i>
                        <p>No services found.</p>
                        <a href="index.php?page=services&action=create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Add First Service
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Service Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($services as $svc): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($svc['name']); ?></strong>
                                        <?php if ($svc['description']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($svc['description']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($svc['category_name'] ?? 'Uncategorized'); ?></td>
                                    <td><?php echo formatCurrency($svc['price']); ?></td>
                                    <td><?php echo $svc['duration']; ?> mins</td>
                                    <td>
                                        <span class="badge bg-<?php echo $svc['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($svc['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="index.php?page=services&action=edit&id=<?php echo $svc['id']; ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="index.php?page=services&action=delete&id=<?php echo $svc['id']; ?>" 
                                               class="btn btn-outline-danger btn-delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif ($action == 'create' || $action == 'edit'): ?>
        <!-- Service Form -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <?php echo $action == 'create' ? 'Add New Service' : 'Edit Service'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Service Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($service->name ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide a service name.</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="category_id" class="form-label">Category *</label>
                                    <select class="form-control" id="category_id" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo $cat['id']; ?>" 
                                                <?php echo ($service->category_id ?? '') == $cat['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">Please select a category.</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="price" class="form-label">Price (₹) *</label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="<?php echo htmlspecialchars($service->price ?? ''); ?>" 
                                           step="0.01" min="0" required>
                                    <div class="invalid-feedback">Please provide a valid price.</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="duration" class="form-label">Duration (minutes) *</label>
                                    <input type="number" class="form-control" id="duration" name="duration" 
                                           value="<?php echo htmlspecialchars($service->duration ?? ''); ?>" 
                                           min="1" required>
                                    <div class="invalid-feedback">Please provide duration in minutes.</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="Brief description of the service"><?php echo htmlspecialchars($service->description ?? ''); ?></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="index.php?page=services" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to List
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    <?php echo $action == 'create' ? 'Create Service' : 'Update Service'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
