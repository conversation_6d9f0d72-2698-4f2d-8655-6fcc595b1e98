<?php
/**
 * Salon Settings Page
 * Salon/Parlour Management System
 */

// Check admin access
if (!hasRole('admin')) {
    redirect('index.php?page=dashboard');
}

$action = $_GET['action'] ?? 'general';
$message = '';
$error = '';

// Sample settings data
$salon_settings = [
    'salon_name' => 'Beauty Salon',
    'salon_address' => '123 Beauty Street, City, State 12345',
    'salon_phone' => '+91 98765 43210',
    'salon_email' => '<EMAIL>',
    'salon_website' => 'www.beautysalon.com',
    'opening_hours' => [
        'monday' => ['open' => '09:00', 'close' => '20:00'],
        'tuesday' => ['open' => '09:00', 'close' => '20:00'],
        'wednesday' => ['open' => '09:00', 'close' => '20:00'],
        'thursday' => ['open' => '09:00', 'close' => '20:00'],
        'friday' => ['open' => '09:00', 'close' => '20:00'],
        'saturday' => ['open' => '09:00', 'close' => '20:00'],
        'sunday' => ['open' => '10:00', 'close' => '18:00']
    ],
    'booking_settings' => [
        'advance_booking_days' => 30,
        'cancellation_hours' => 24,
        'slot_duration' => 30,
        'auto_confirm' => false
    ],
    'payment_settings' => [
        'currency' => 'INR',
        'tax_rate' => 18.0,
        'payment_methods' => ['cash', 'card', 'upi', 'wallet']
    ],
    'notification_settings' => [
        'email_notifications' => true,
        'sms_notifications' => true,
        'booking_reminders' => true,
        'promotional_emails' => true
    ]
];
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-cog"></i>
        Salon Settings
    </h1>
</div>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Settings Categories
                    </h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="index.php?page=settings&action=general" 
                       class="list-group-item list-group-item-action <?php echo $action == 'general' ? 'active' : ''; ?>">
                        <i class="fas fa-info-circle me-2"></i>
                        General Information
                    </a>
                    <a href="index.php?page=settings&action=hours" 
                       class="list-group-item list-group-item-action <?php echo $action == 'hours' ? 'active' : ''; ?>">
                        <i class="fas fa-clock me-2"></i>
                        Operating Hours
                    </a>
                    <a href="index.php?page=settings&action=booking" 
                       class="list-group-item list-group-item-action <?php echo $action == 'booking' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar me-2"></i>
                        Booking Settings
                    </a>
                    <a href="index.php?page=settings&action=payment" 
                       class="list-group-item list-group-item-action <?php echo $action == 'payment' ? 'active' : ''; ?>">
                        <i class="fas fa-credit-card me-2"></i>
                        Payment Settings
                    </a>
                    <a href="index.php?page=settings&action=notifications" 
                       class="list-group-item list-group-item-action <?php echo $action == 'notifications' ? 'active' : ''; ?>">
                        <i class="fas fa-bell me-2"></i>
                        Notifications
                    </a>
                    <a href="index.php?page=settings&action=backup" 
                       class="list-group-item list-group-item-action <?php echo $action == 'backup' ? 'active' : ''; ?>">
                        <i class="fas fa-database me-2"></i>
                        Backup & Security
                    </a>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-lg-9">
            <?php if ($action == 'general'): ?>
            <!-- General Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        General Information
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Salon Name</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($salon_settings['salon_name']); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" value="<?php echo htmlspecialchars($salon_settings['salon_email']); ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" value="<?php echo htmlspecialchars($salon_settings['salon_phone']); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Website</label>
                                <input type="url" class="form-control" value="<?php echo htmlspecialchars($salon_settings['salon_website']); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Address</label>
                            <textarea class="form-control" rows="3"><?php echo htmlspecialchars($salon_settings['salon_address']); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Changes
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($action == 'hours'): ?>
            <!-- Operating Hours -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Operating Hours
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <?php foreach ($salon_settings['opening_hours'] as $day => $hours): ?>
                        <div class="row align-items-center mb-3">
                            <div class="col-md-3">
                                <label class="form-label"><?php echo ucfirst($day); ?></label>
                            </div>
                            <div class="col-md-3">
                                <input type="time" class="form-control" value="<?php echo $hours['open']; ?>">
                            </div>
                            <div class="col-md-1 text-center">
                                <span class="text-muted">to</span>
                            </div>
                            <div class="col-md-3">
                                <input type="time" class="form-control" value="<?php echo $hours['close']; ?>">
                            </div>
                            <div class="col-md-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">Open</label>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Hours
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($action == 'booking'): ?>
            <!-- Booking Settings -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>
                        Booking Settings
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Advance Booking (Days)</label>
                                <input type="number" class="form-control" value="<?php echo $salon_settings['booking_settings']['advance_booking_days']; ?>">
                                <div class="form-text">How many days in advance customers can book</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Cancellation Notice (Hours)</label>
                                <input type="number" class="form-control" value="<?php echo $salon_settings['booking_settings']['cancellation_hours']; ?>">
                                <div class="form-text">Minimum hours before appointment for cancellation</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Time Slot Duration (Minutes)</label>
                                <select class="form-control">
                                    <option value="15">15 minutes</option>
                                    <option value="30" selected>30 minutes</option>
                                    <option value="45">45 minutes</option>
                                    <option value="60">60 minutes</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" <?php echo $salon_settings['booking_settings']['auto_confirm'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label">Auto-confirm bookings</label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Settings
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($action == 'payment'): ?>
            <!-- Payment Settings -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        Payment Settings
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Currency</label>
                                <select class="form-control">
                                    <option value="INR" selected>Indian Rupee (₹)</option>
                                    <option value="USD">US Dollar ($)</option>
                                    <option value="EUR">Euro (€)</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Tax Rate (%)</label>
                                <input type="number" class="form-control" step="0.01" value="<?php echo $salon_settings['payment_settings']['tax_rate']; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Accepted Payment Methods</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" checked>
                                        <label class="form-check-label">Cash</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" checked>
                                        <label class="form-check-label">Credit/Debit Card</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" checked>
                                        <label class="form-check-label">UPI</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" checked>
                                        <label class="form-check-label">Digital Wallet</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Settings
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($action == 'notifications'): ?>
            <!-- Notification Settings -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        Notification Settings
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Email Notifications</h6>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">New booking notifications</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">Booking confirmations</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">Cancellation notifications</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox">
                                    <label class="form-check-label">Promotional emails</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>SMS Notifications</h6>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">Booking reminders</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox">
                                    <label class="form-check-label">Promotional SMS</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" checked>
                                    <label class="form-check-label">Payment confirmations</label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Settings
                        </button>
                    </form>
                </div>
            </div>

            <?php else: ?>
            <!-- Backup & Security -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        Backup & Security
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <h6>Database Backup</h6>
                            <p class="text-muted">Last backup: January 15, 2024 at 2:30 AM</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="createBackup()">
                                    <i class="fas fa-download me-1"></i>
                                    Create Backup Now
                                </button>
                                <button class="btn btn-outline-primary" onclick="scheduleBackup()">
                                    <i class="fas fa-clock me-1"></i>
                                    Schedule Auto Backup
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <h6>Security Settings</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">Two-factor authentication</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">Login attempt monitoring</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox">
                                <label class="form-check-label">IP address restrictions</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function createBackup() {
    if (confirm('Create a database backup now? This may take a few minutes.')) {
        alert('Backup created successfully! (Feature coming soon)');
    }
}

function scheduleBackup() {
    alert('Backup scheduling (Feature coming soon)');
}
</script>
