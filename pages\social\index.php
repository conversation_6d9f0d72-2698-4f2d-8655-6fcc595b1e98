<?php
/**
 * Social Media Management Page
 * Salon/Parlour Management System
 */

$action = $_GET['action'] ?? 'dashboard';
$message = '';
$error = '';

// Sample social media data
$social_stats = [
    'instagram' => [
        'followers' => 12500,
        'posts' => 245,
        'engagement' => 8.5,
        'growth' => 15.2
    ],
    'facebook' => [
        'followers' => 8900,
        'posts' => 189,
        'engagement' => 6.8,
        'growth' => 12.1
    ],
    'youtube' => [
        'followers' => 3400,
        'posts' => 45,
        'engagement' => 12.3,
        'growth' => 22.5
    ],
    'tiktok' => [
        'followers' => 15600,
        'posts' => 78,
        'engagement' => 18.7,
        'growth' => 35.8
    ]
];

$recent_posts = [
    [
        'id' => 1,
        'platform' => 'instagram',
        'content' => 'Stunning bridal transformation! ✨ Our bride looked absolutely radiant on her special day. 💕 #BridalMakeup #WeddingGlow',
        'image' => 'post-1.jpg',
        'likes' => 245,
        'comments' => 18,
        'shares' => 12,
        'date' => '2024-01-15 14:30:00',
        'status' => 'published'
    ],
    [
        'id' => 2,
        'platform' => 'facebook',
        'content' => 'New Year, New Hair! 💇‍♀️ Book your hair transformation appointment today and start 2024 with a fresh new look!',
        'image' => 'post-2.jpg',
        'likes' => 89,
        'comments' => 7,
        'shares' => 23,
        'date' => '2024-01-12 10:15:00',
        'status' => 'published'
    ],
    [
        'id' => 3,
        'platform' => 'tiktok',
        'content' => 'Quick makeup transformation tutorial! From natural to glam in 60 seconds ⏰✨ #MakeupTutorial #GlowUp',
        'image' => 'post-3.jpg',
        'likes' => 1250,
        'comments' => 89,
        'shares' => 156,
        'date' => '2024-01-10 16:45:00',
        'status' => 'published'
    ],
    [
        'id' => 4,
        'platform' => 'instagram',
        'content' => 'Self-care Sunday vibes! 🧘‍♀️ Treat yourself to our relaxing spa package this weekend. You deserve it! 💆‍♀️',
        'image' => 'post-4.jpg',
        'likes' => 156,
        'comments' => 12,
        'shares' => 8,
        'date' => '2024-01-08 09:00:00',
        'status' => 'scheduled'
    ]
];

$content_ideas = [
    'Before & After transformations',
    'Behind-the-scenes salon life',
    'Beauty tips and tutorials',
    'Client testimonials',
    'Product recommendations',
    'Seasonal beauty trends',
    'Staff spotlights',
    'Special offers and promotions'
];
?>

<!-- Mobile Sidebar Toggle -->
<button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<!-- Top Header -->
<div class="top-header">
    <h1 class="page-title">
        <i class="fas fa-share-alt"></i>
        Social Media
    </h1>
    <div class="header-actions">
        <?php if (hasRole('admin') || hasRole('staff')): ?>
        <a href="index.php?page=social&action=create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Create Post
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="container-fluid">
    <!-- Social Media Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card social-card instagram-card">
                <div class="card-body">
                    <div class="social-header">
                        <i class="fab fa-instagram fa-2x"></i>
                        <div class="social-growth">
                            <span class="growth-indicator positive">
                                <i class="fas fa-arrow-up"></i>
                                +<?php echo $social_stats['instagram']['growth']; ?>%
                            </span>
                        </div>
                    </div>
                    <h4><?php echo number_format($social_stats['instagram']['followers']); ?></h4>
                    <p class="text-muted">Instagram Followers</p>
                    <div class="social-metrics">
                        <small>
                            <i class="fas fa-heart me-1"></i>
                            <?php echo $social_stats['instagram']['engagement']; ?>% engagement
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card social-card facebook-card">
                <div class="card-body">
                    <div class="social-header">
                        <i class="fab fa-facebook fa-2x"></i>
                        <div class="social-growth">
                            <span class="growth-indicator positive">
                                <i class="fas fa-arrow-up"></i>
                                +<?php echo $social_stats['facebook']['growth']; ?>%
                            </span>
                        </div>
                    </div>
                    <h4><?php echo number_format($social_stats['facebook']['followers']); ?></h4>
                    <p class="text-muted">Facebook Followers</p>
                    <div class="social-metrics">
                        <small>
                            <i class="fas fa-thumbs-up me-1"></i>
                            <?php echo $social_stats['facebook']['engagement']; ?>% engagement
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card social-card youtube-card">
                <div class="card-body">
                    <div class="social-header">
                        <i class="fab fa-youtube fa-2x"></i>
                        <div class="social-growth">
                            <span class="growth-indicator positive">
                                <i class="fas fa-arrow-up"></i>
                                +<?php echo $social_stats['youtube']['growth']; ?>%
                            </span>
                        </div>
                    </div>
                    <h4><?php echo number_format($social_stats['youtube']['followers']); ?></h4>
                    <p class="text-muted">YouTube Subscribers</p>
                    <div class="social-metrics">
                        <small>
                            <i class="fas fa-play me-1"></i>
                            <?php echo $social_stats['youtube']['engagement']; ?>% engagement
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card social-card tiktok-card">
                <div class="card-body">
                    <div class="social-header">
                        <i class="fab fa-tiktok fa-2x"></i>
                        <div class="social-growth">
                            <span class="growth-indicator positive">
                                <i class="fas fa-arrow-up"></i>
                                +<?php echo $social_stats['tiktok']['growth']; ?>%
                            </span>
                        </div>
                    </div>
                    <h4><?php echo number_format($social_stats['tiktok']['followers']); ?></h4>
                    <p class="text-muted">TikTok Followers</p>
                    <div class="social-metrics">
                        <small>
                            <i class="fas fa-video me-1"></i>
                            <?php echo $social_stats['tiktok']['engagement']; ?>% engagement
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Posts -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Recent Posts
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($recent_posts as $post): ?>
                    <div class="social-post">
                        <div class="post-header">
                            <div class="platform-info">
                                <i class="fab fa-<?php echo $post['platform']; ?> platform-icon platform-<?php echo $post['platform']; ?>"></i>
                                <div class="post-meta">
                                    <h6 class="mb-1"><?php echo ucfirst($post['platform']); ?></h6>
                                    <small class="text-muted"><?php echo formatDateTime($post['date']); ?></small>
                                </div>
                            </div>
                            <div class="post-status">
                                <span class="status-badge status-<?php echo $post['status']; ?>">
                                    <?php echo ucfirst($post['status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="post-content">
                            <p><?php echo htmlspecialchars($post['content']); ?></p>
                            <div class="post-image-placeholder">
                                <i class="fas fa-image fa-2x"></i>
                                <small>Image: <?php echo $post['image']; ?></small>
                            </div>
                        </div>
                        
                        <div class="post-metrics">
                            <div class="metric">
                                <i class="fas fa-heart text-danger"></i>
                                <span><?php echo number_format($post['likes']); ?></span>
                            </div>
                            <div class="metric">
                                <i class="fas fa-comment text-primary"></i>
                                <span><?php echo number_format($post['comments']); ?></span>
                            </div>
                            <div class="metric">
                                <i class="fas fa-share text-success"></i>
                                <span><?php echo number_format($post['shares']); ?></span>
                            </div>
                        </div>
                        
                        <?php if (hasRole('admin') || hasRole('staff')): ?>
                        <div class="post-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="editPost(<?php echo $post['id']; ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="boostPost(<?php echo $post['id']; ?>)">
                                <i class="fas fa-rocket"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deletePost(<?php echo $post['id']; ?>)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- Content Ideas & Tools -->
        <div class="col-lg-4">
            <!-- Content Ideas -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Content Ideas
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($content_ideas as $idea): ?>
                    <div class="content-idea">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <?php echo $idea; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="schedulePost()">
                            <i class="fas fa-calendar me-1"></i>
                            Schedule Post
                        </button>
                        <button class="btn btn-outline-primary" onclick="viewAnalytics()">
                            <i class="fas fa-chart-bar me-1"></i>
                            View Analytics
                        </button>
                        <button class="btn btn-outline-primary" onclick="manageHashtags()">
                            <i class="fas fa-hashtag me-1"></i>
                            Manage Hashtags
                        </button>
                        <button class="btn btn-outline-primary" onclick="respondToComments()">
                            <i class="fas fa-reply me-1"></i>
                            Respond to Comments
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Social Media Tips -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tips me-2"></i>
                        Social Media Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="tip-item">
                        <h6>Best Posting Times</h6>
                        <p class="small text-muted">Instagram: 11 AM - 1 PM<br>Facebook: 1 PM - 3 PM<br>TikTok: 6 AM - 10 AM</p>
                    </div>
                    <div class="tip-item">
                        <h6>Hashtag Strategy</h6>
                        <p class="small text-muted">Use 5-10 relevant hashtags. Mix popular and niche tags for better reach.</p>
                    </div>
                    <div class="tip-item">
                        <h6>Engagement</h6>
                        <p class="small text-muted">Respond to comments within 2 hours for better algorithm performance.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.social-card {
    border: none;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.social-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.instagram-card {
    background: linear-gradient(135deg, #E1306C, #F56040);
    color: white;
}

.facebook-card {
    background: linear-gradient(135deg, #1877F2, #42A5F5);
    color: white;
}

.youtube-card {
    background: linear-gradient(135deg, #FF0000, #FF5722);
    color: white;
}

.tiktok-card {
    background: linear-gradient(135deg, #000000, #FF0050);
    color: white;
}

.social-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.growth-indicator {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.growth-indicator.positive {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.social-metrics {
    margin-top: 10px;
}

.social-post {
    border-bottom: 1px solid #eee;
    padding: 20px 0;
}

.social-post:last-child {
    border-bottom: none;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.platform-info {
    display: flex;
    align-items: center;
}

.platform-icon {
    font-size: 24px;
    margin-right: 12px;
}

.platform-instagram { color: #E1306C; }
.platform-facebook { color: #1877F2; }
.platform-youtube { color: #FF0000; }
.platform-tiktok { color: #000000; }

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-published {
    background: var(--success-color);
    color: white;
}

.status-scheduled {
    background: var(--warning-color);
    color: white;
}

.post-content {
    margin-bottom: 15px;
}

.post-image-placeholder {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: #6c757d;
    margin-top: 10px;
}

.post-metrics {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.post-actions {
    display: flex;
    gap: 10px;
}

.content-idea {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.content-idea:last-child {
    border-bottom: none;
}

.tip-item {
    margin-bottom: 20px;
}

.tip-item:last-child {
    margin-bottom: 0;
}

.tip-item h6 {
    color: var(--primary-color);
    margin-bottom: 5px;
}
</style>

<script>
function editPost(postId) {
    window.location.href = `index.php?page=social&action=edit&id=${postId}`;
}

function boostPost(postId) {
    alert('Post boost feature (Coming soon)');
}

function deletePost(postId) {
    if (confirm('Are you sure you want to delete this post?')) {
        alert('Post deleted (Feature coming soon)');
    }
}

function schedulePost() {
    window.location.href = 'index.php?page=social&action=schedule';
}

function viewAnalytics() {
    alert('Social media analytics (Feature coming soon)');
}

function manageHashtags() {
    alert('Hashtag management (Feature coming soon)');
}

function respondToComments() {
    alert('Comment management (Feature coming soon)');
}
</script>
