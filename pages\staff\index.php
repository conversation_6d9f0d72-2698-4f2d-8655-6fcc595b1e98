<?php
/**
 * Staff Management Page
 * Salon/Parlour Management System
 */

require_once 'classes/Staff.php';
require_once 'classes/User.php';

// Check admin access
if (!hasRole('admin')) {
    redirect('index.php?page=dashboard');
}

$database = new Database();
$db = $database->getConnection();

$staff = new Staff($db);
$user = new User($db);

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle actions
switch ($action) {
    case 'create':
        if ($_POST) {
            // First create user account
            $user->name = $_POST['name'];
            $user->email = $_POST['email'];
            $user->phone = $_POST['phone'];
            $user->password = $_POST['password'];
            $user->role = 'staff';

            // Check if email exists
            if ($user->emailExists()) {
                $error = "Email address already exists.";
            } else {
                if ($user->create()) {
                    // Create staff record
                    $staff->user_id = $user->id;
                    $staff->specialization = $_POST['specialization'];
                    $staff->shift_start = $_POST['shift_start'];
                    $staff->shift_end = $_POST['shift_end'];
                    $staff->commission_rate = $_POST['commission_rate'];

                    if ($staff->create()) {
                        $message = "Staff member created successfully!";
                        $action = 'list';
                    } else {
                        $error = "Failed to create staff record.";
                        // Delete the user account if staff creation failed
                        $user->id = $user->id;
                        $user->delete();
                    }
                } else {
                    $error = "Failed to create user account.";
                }
            }
        }
        break;

    case 'edit':
        $staff_id = $_GET['id'] ?? 0;
        $staff->id = $staff_id;
        
        if ($_POST) {
            // Update user information
            $staff_data = $staff->readOne();
            $user->id = $staff_data['user_id'];
            $user->name = $_POST['name'];
            $user->email = $_POST['email'];
            $user->phone = $_POST['phone'];

            // Update staff information
            $staff->specialization = $_POST['specialization'];
            $staff->shift_start = $_POST['shift_start'];
            $staff->shift_end = $_POST['shift_end'];
            $staff->commission_rate = $_POST['commission_rate'];

            if ($user->update() && $staff->update()) {
                $message = "Staff member updated successfully!";
                $action = 'list';
            } else {
                $error = "Failed to update staff member.";
            }
        } else {
            $staff->readOne();
        }
        break;

    case 'delete':
        $staff_id = $_GET['id'] ?? 0;
        $staff->id = $staff_id;
        
        if ($staff->delete()) {
            $message = "Staff member deleted successfully!";
        } else {
            $error = "Failed to delete staff member.";
        }
        $action = 'list';
        break;

    case 'view':
        $staff_id = $_GET['id'] ?? 0;
        $staff->id = $staff_id;
        $staff_data = $staff->readOne();
        
        // Get performance data
        $performance = $staff->getPerformance($staff_id);
        
        // Get today's schedule
        $today_schedule = $staff->getSchedule($staff_id, date('Y-m-d'));
        break;
}

// Get data for list view
if ($action == 'list') {
    $staff_result = $staff->readAll();
    $staff_members = $staff_result->fetchAll();
}
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-users me-2"></i>
                    Staff Management
                </h1>
                <?php if ($action == 'list'): ?>
                <a href="index.php?page=staff&action=create" class="btn btn-primary">
                    <i class="fas fa-user-plus me-1"></i>
                    Add New Staff
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- Staff List -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">All Staff Members</h5>
            </div>
            <div class="card-body">
                <?php if (empty($staff_members)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>No staff members found.</p>
                        <a href="index.php?page=staff&action=create" class="btn btn-primary">
                            <i class="fas fa-user-plus me-1"></i>
                            Add First Staff Member
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Contact</th>
                                    <th>Specialization</th>
                                    <th>Shift</th>
                                    <th>Commission</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($staff_members as $member): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($member['name']); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($member['email']); ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($member['phone']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($member['specialization']); ?></td>
                                    <td>
                                        <?php echo date('h:i A', strtotime($member['shift_start'])); ?> - 
                                        <?php echo date('h:i A', strtotime($member['shift_end'])); ?>
                                    </td>
                                    <td><?php echo $member['commission_rate']; ?>%</td>
                                    <td>
                                        <span class="badge bg-<?php echo $member['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($member['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="index.php?page=staff&action=view&id=<?php echo $member['id']; ?>" 
                                               class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="index.php?page=staff&action=edit&id=<?php echo $member['id']; ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="index.php?page=staff&action=delete&id=<?php echo $member['id']; ?>" 
                                               class="btn btn-outline-danger btn-delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif ($action == 'create' || $action == 'edit'): ?>
        <!-- Staff Form -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <?php echo $action == 'create' ? 'Add New Staff Member' : 'Edit Staff Member'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($staff_data['name'] ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide the staff member's name.</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($staff_data['email'] ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($staff_data['phone'] ?? ''); ?>">
                                </div>
                                
                                <?php if ($action == 'create'): ?>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           minlength="6" required>
                                    <div class="invalid-feedback">Password must be at least 6 characters.</div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="specialization" class="form-label">Specialization</label>
                                <input type="text" class="form-control" id="specialization" name="specialization" 
                                       value="<?php echo htmlspecialchars($staff->specialization ?? ''); ?>"
                                       placeholder="e.g., Hair Styling, Makeup, Facial Treatments">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="shift_start" class="form-label">Shift Start Time</label>
                                    <input type="time" class="form-control" id="shift_start" name="shift_start" 
                                           value="<?php echo htmlspecialchars($staff->shift_start ?? '09:00'); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="shift_end" class="form-label">Shift End Time</label>
                                    <input type="time" class="form-control" id="shift_end" name="shift_end" 
                                           value="<?php echo htmlspecialchars($staff->shift_end ?? '18:00'); ?>">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                                <input type="number" class="form-control" id="commission_rate" name="commission_rate" 
                                       value="<?php echo htmlspecialchars($staff->commission_rate ?? '15'); ?>" 
                                       step="0.01" min="0" max="100">
                                <div class="form-text">Percentage of service revenue as commission</div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="index.php?page=staff" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to List
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    <?php echo $action == 'create' ? 'Create Staff Member' : 'Update Staff Member'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    <?php elseif ($action == 'view'): ?>
        <!-- Staff Details -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Staff Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Personal Information</h6>
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($staff_data['name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($staff_data['email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($staff_data['phone']); ?></p>
                                <p><strong>Status:</strong> 
                                    <span class="badge bg-<?php echo $staff_data['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($staff_data['status']); ?>
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6>Work Information</h6>
                                <p><strong>Specialization:</strong> <?php echo htmlspecialchars($staff_data['specialization']); ?></p>
                                <p><strong>Shift:</strong> 
                                    <?php echo date('h:i A', strtotime($staff_data['shift_start'])); ?> - 
                                    <?php echo date('h:i A', strtotime($staff_data['shift_end'])); ?>
                                </p>
                                <p><strong>Commission Rate:</strong> <?php echo $staff_data['commission_rate']; ?>%</p>
                                <p><strong>Joined:</strong> <?php echo formatDate($staff_data['created_at']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Schedule -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">Today's Schedule</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($today_schedule)): ?>
                            <p class="text-muted">No appointments scheduled for today.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Customer</th>
                                            <th>Contact</th>
                                            <th>Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($today_schedule as $appointment): ?>
                                        <tr>
                                            <td><?php echo date('h:i A', strtotime($appointment['booking_time'])); ?></td>
                                            <td><?php echo htmlspecialchars($appointment['customer_name']); ?></td>
                                            <td><?php echo htmlspecialchars($appointment['customer_phone']); ?></td>
                                            <td><?php echo formatCurrency($appointment['total_amount']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h3 class="text-primary"><?php echo $performance['total_bookings'] ?? 0; ?></h3>
                            <p class="text-muted">Total Bookings</p>
                        </div>
                        
                        <div class="text-center mb-3">
                            <h4 class="text-success"><?php echo formatCurrency($performance['total_revenue'] ?? 0); ?></h4>
                            <p class="text-muted">Total Revenue</p>
                        </div>
                        
                        <div class="text-center mb-3">
                            <h4 class="text-info"><?php echo formatCurrency($performance['commission_earned'] ?? 0); ?></h4>
                            <p class="text-muted">Commission Earned</p>
                        </div>
                        
                        <div class="text-center">
                            <h5 class="text-warning"><?php echo formatCurrency($performance['avg_booking_value'] ?? 0); ?></h5>
                            <p class="text-muted">Avg. Booking Value</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="index.php?page=staff" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List
                    </a>
                    <a href="index.php?page=staff&action=edit&id=<?php echo $staff_data['id']; ?>" 
                       class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
