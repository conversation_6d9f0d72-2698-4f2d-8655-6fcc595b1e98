<?php
/**
 * Sample Data Setup Script
 * Salon/Parlour Management System
 */

require_once 'config/config.php';
require_once 'classes/User.php';
require_once 'classes/Staff.php';
require_once 'classes/Service.php';

$database = new Database();
$db = $database->getConnection();

$user = new User($db);
$staff = new Staff($db);
$service = new Service($db);

echo "<h2>Setting up sample data...</h2>";

// Create sample staff users
$staff_members = [
    [
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 43211',
        'password' => 'password123',
        'specialization' => 'Bridal Makeup & Hair Styling',
        'shift_start' => '09:00',
        'shift_end' => '18:00',
        'commission_rate' => 15.0
    ],
    [
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 43212',
        'password' => 'password123',
        'specialization' => 'Hair Care & Treatments',
        'shift_start' => '10:00',
        'shift_end' => '19:00',
        'commission_rate' => 12.0
    ],
    [
        'name' => '<PERSON> <PERSON>',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 43213',
        'password' => 'password123',
        'specialization' => 'Facial & Skincare',
        'shift_start' => '09:30',
        'shift_end' => '18:30',
        'commission_rate' => 18.0
    ],
    [
        'name' => 'Sophie Chen',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 43214',
        'password' => 'password123',
        'specialization' => 'Nail Art & Manicure',
        'shift_start' => '11:00',
        'shift_end' => '20:00',
        'commission_rate' => 10.0
    ],
    [
        'name' => 'Anna Thompson',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 43215',
        'password' => 'password123',
        'specialization' => 'Spa & Massage Therapy',
        'shift_start' => '08:00',
        'shift_end' => '17:00',
        'commission_rate' => 20.0
    ]
];

echo "<h3>Creating staff members...</h3>";
foreach ($staff_members as $member) {
    // Check if user already exists
    if (!$user->emailExists($member['email'])) {
        // Create user account
        $user->name = $member['name'];
        $user->email = $member['email'];
        $user->phone = $member['phone'];
        $user->password = $member['password'];
        $user->role = 'staff';
        
        if ($user->create()) {
            // Create staff record
            $staff->user_id = $user->id;
            $staff->specialization = $member['specialization'];
            $staff->shift_start = $member['shift_start'];
            $staff->shift_end = $member['shift_end'];
            $staff->commission_rate = $member['commission_rate'];
            
            if ($staff->create()) {
                echo "✅ Created staff member: " . $member['name'] . "<br>";
            } else {
                echo "❌ Failed to create staff record for: " . $member['name'] . "<br>";
            }
        } else {
            echo "❌ Failed to create user account for: " . $member['name'] . "<br>";
        }
    } else {
        echo "ℹ️ Staff member already exists: " . $member['name'] . "<br>";
    }
}

// Create sample services if they don't exist
$sample_services = [
    [
        'name' => 'Basic Haircut',
        'description' => 'Professional hair cutting and styling',
        'price' => 500,
        'duration' => 45,
        'category' => 'Hair Care'
    ],
    [
        'name' => 'Hair Wash & Blow Dry',
        'description' => 'Hair washing with professional blow dry',
        'price' => 300,
        'duration' => 30,
        'category' => 'Hair Care'
    ],
    [
        'name' => 'Basic Facial',
        'description' => 'Cleansing and moisturizing facial treatment',
        'price' => 800,
        'duration' => 60,
        'category' => 'Skincare'
    ],
    [
        'name' => 'Bridal Makeup',
        'description' => 'Complete bridal makeup with trial',
        'price' => 3500,
        'duration' => 120,
        'category' => 'Makeup'
    ],
    [
        'name' => 'Manicure',
        'description' => 'Basic nail care and polish',
        'price' => 400,
        'duration' => 45,
        'category' => 'Nail Care'
    ],
    [
        'name' => 'Pedicure',
        'description' => 'Foot care and nail polish',
        'price' => 500,
        'duration' => 60,
        'category' => 'Nail Care'
    ],
    [
        'name' => 'Full Body Massage',
        'description' => 'Relaxing full body massage therapy',
        'price' => 1200,
        'duration' => 90,
        'category' => 'Spa'
    ],
    [
        'name' => 'Party Makeup',
        'description' => 'Glamorous makeup for special occasions',
        'price' => 1500,
        'duration' => 75,
        'category' => 'Makeup'
    ]
];

echo "<h3>Creating sample services...</h3>";
foreach ($sample_services as $svc) {
    // Check if service already exists
    $query = "SELECT COUNT(*) as count FROM services WHERE name = :name";
    $stmt = $db->prepare($query);
    $stmt->bindParam(":name", $svc['name']);
    $stmt->execute();
    
    if ($stmt->fetch()['count'] == 0) {
        $service->name = $svc['name'];
        $service->description = $svc['description'];
        $service->price = $svc['price'];
        $service->duration = $svc['duration'];
        $service->category = $svc['category'];
        $service->status = 'active';
        
        if ($service->create()) {
            echo "✅ Created service: " . $svc['name'] . "<br>";
        } else {
            echo "❌ Failed to create service: " . $svc['name'] . "<br>";
        }
    } else {
        echo "ℹ️ Service already exists: " . $svc['name'] . "<br>";
    }
}

// Create sample customers
$sample_customers = [
    [
        'name' => 'Sarah Johnson',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 54321',
        'password' => 'customer123'
    ],
    [
        'name' => 'Emma Wilson',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 54322',
        'password' => 'customer123'
    ],
    [
        'name' => 'Lisa Brown',
        'email' => '<EMAIL>',
        'phone' => '+91 98765 54323',
        'password' => 'customer123'
    ]
];

echo "<h3>Creating sample customers...</h3>";
foreach ($sample_customers as $customer) {
    if (!$user->emailExists($customer['email'])) {
        $user->name = $customer['name'];
        $user->email = $customer['email'];
        $user->phone = $customer['phone'];
        $user->password = $customer['password'];
        $user->role = 'customer';
        
        if ($user->create()) {
            echo "✅ Created customer: " . $customer['name'] . "<br>";
        } else {
            echo "❌ Failed to create customer: " . $customer['name'] . "<br>";
        }
    } else {
        echo "ℹ️ Customer already exists: " . $customer['name'] . "<br>";
    }
}

echo "<h2>✅ Sample data setup completed!</h2>";
echo "<p><a href='index.php'>Go to Application</a></p>";
?>
