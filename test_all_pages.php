<?php
/**
 * Test All Pages Script
 * Tests all pages to ensure they work without errors
 */

require_once 'config/config.php';

// List of all pages to test
$pages_to_test = [
    'home' => 'Home Page',
    'login' => 'Login Page',
    'register' => 'Register Page',
    'dashboard' => 'Dashboard',
    'bookings' => 'Appointments',
    'services' => 'Services & Treatments',
    'packages' => 'Beauty Packages',
    'products' => 'Beauty Products',
    'customers' => 'Clients',
    'loyalty' => 'Loyalty Program',
    'reviews' => 'Reviews & Ratings',
    'staff' => 'Beauty Experts',
    'invoices' => 'Billing & Invoices',
    'inventory' => 'Inventory',
    'reports' => 'Analytics & Reports',
    'promotions' => 'Offers & Discounts',
    'gallery' => 'Beauty Gallery',
    'social' => 'Social Media',
    'settings' => 'Salon Settings',
    'help' => 'Help & Support',
    'profile' => 'My Profile'
];

echo "<h1>Testing All Pages</h1>";
echo "<p>This script tests all pages to ensure they load without fatal errors.</p>";

$total_pages = count($pages_to_test);
$successful_pages = 0;
$failed_pages = [];

foreach ($pages_to_test as $page => $title) {
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<h3>Testing: $title ($page)</h3>";
    
    // Start output buffering to catch any errors
    ob_start();
    $error_occurred = false;
    
    try {
        // Set error handler to catch any errors
        set_error_handler(function($severity, $message, $file, $line) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        });
        
        // Simulate the page loading process
        $_GET['page'] = $page;
        
        // Check if page file exists
        $page_file = '';
        switch ($page) {
            case 'home':
                $page_file = 'pages/home.php';
                break;
            case 'login':
                $page_file = 'pages/auth/login.php';
                break;
            case 'register':
                $page_file = 'pages/auth/register.php';
                break;
            case 'dashboard':
                $page_file = 'pages/dashboard/index.php';
                break;
            case 'bookings':
                $page_file = 'pages/bookings/index.php';
                break;
            case 'services':
                $page_file = 'pages/services/index.php';
                break;
            case 'packages':
                $page_file = 'pages/packages/index.php';
                break;
            case 'products':
                $page_file = 'pages/products/index.php';
                break;
            case 'customers':
                $page_file = 'pages/customers/index.php';
                break;
            case 'loyalty':
                $page_file = 'pages/loyalty/index.php';
                break;
            case 'reviews':
                $page_file = 'pages/reviews/index.php';
                break;
            case 'staff':
                $page_file = 'pages/staff/index.php';
                break;
            case 'invoices':
                $page_file = 'pages/invoices/index.php';
                break;
            case 'inventory':
                $page_file = 'pages/inventory/index.php';
                break;
            case 'reports':
                $page_file = 'pages/reports/index.php';
                break;
            case 'promotions':
                $page_file = 'pages/promotions/index.php';
                break;
            case 'gallery':
                $page_file = 'pages/gallery/index.php';
                break;
            case 'social':
                $page_file = 'pages/social/index.php';
                break;
            case 'settings':
                $page_file = 'pages/settings/index.php';
                break;
            case 'help':
                $page_file = 'pages/help/index.php';
                break;
            case 'profile':
                $page_file = 'pages/profile/index.php';
                break;
        }
        
        if (file_exists($page_file)) {
            // Mock session for testing
            if (!isset($_SESSION)) {
                session_start();
            }
            $_SESSION['user_id'] = 1;
            $_SESSION['name'] = 'Test User';
            $_SESSION['role'] = 'admin';
            $_SESSION['email'] = '<EMAIL>';
            
            // Include the page file
            include $page_file;
            
            echo "<span style='color: green;'>✅ SUCCESS</span> - Page loaded without errors";
            $successful_pages++;
        } else {
            echo "<span style='color: red;'>❌ FAILED</span> - Page file not found: $page_file";
            $failed_pages[] = $page;
        }
        
        // Restore error handler
        restore_error_handler();
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>❌ FAILED</span> - Error: " . $e->getMessage();
        $failed_pages[] = $page;
        $error_occurred = true;
        restore_error_handler();
    }
    
    // Clear output buffer
    ob_end_clean();
    
    echo "</div>";
}

echo "<h2>Test Results Summary</h2>";
echo "<div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 20px 0;'>";
echo "<p><strong>Total Pages Tested:</strong> $total_pages</p>";
echo "<p><strong>Successful:</strong> <span style='color: green;'>$successful_pages</span></p>";
echo "<p><strong>Failed:</strong> <span style='color: red;'>" . count($failed_pages) . "</span></p>";

if (count($failed_pages) > 0) {
    echo "<p><strong>Failed Pages:</strong></p>";
    echo "<ul>";
    foreach ($failed_pages as $failed_page) {
        echo "<li>$failed_page</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: green; font-weight: bold;'>🎉 All pages loaded successfully!</p>";
}

$success_rate = ($successful_pages / $total_pages) * 100;
echo "<p><strong>Success Rate:</strong> " . number_format($success_rate, 1) . "%</p>";
echo "</div>";

if ($success_rate >= 90) {
    echo "<div style='padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 10px; color: #155724;'>";
    echo "<h3>🎉 Excellent! Your salon management system is working great!</h3>";
    echo "<p>Most or all pages are loading successfully. You can now:</p>";
    echo "<ul>";
    echo "<li>Run the database fix script: <a href='fix_database.php'>fix_database.php</a></li>";
    echo "<li>Start using the application: <a href='index.php'>Go to Application</a></li>";
    echo "<li><NAME_EMAIL> / password</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 10px; color: #721c24;'>";
    echo "<h3>⚠️ Some issues found</h3>";
    echo "<p>Please check the failed pages and fix any issues before proceeding.</p>";
    echo "</div>";
}

echo "<p style='margin-top: 30px;'><a href='index.php'>← Back to Application</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
